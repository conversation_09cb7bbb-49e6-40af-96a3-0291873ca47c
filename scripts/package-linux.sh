#!/bin/bash

# Linux打包脚本 - 创建便携版tar.gz

echo "📦 开始打包Linux便携版..."

# 检查linux-unpacked目录是否存在
if [ ! -d "dist-electron/linux-unpacked" ]; then
    echo "❌ 错误: linux-unpacked目录不存在，请先运行构建命令"
    echo "   运行: npm run dist:linux"
    exit 1
fi

# 进入dist-electron目录
cd dist-electron

# 获取版本号
VERSION=$(node -p "require('../package.json').version")
PACKAGE_NAME="Infinity Notes-${VERSION}-linux-x64.tar.gz"

echo "📁 正在打包: ${PACKAGE_NAME}"

# 创建tar.gz包
tar -czf "${PACKAGE_NAME}" linux-unpacked/

# 检查是否成功
if [ $? -eq 0 ]; then
    echo "✅ 打包成功!"
    echo "📦 文件位置: dist-electron/${PACKAGE_NAME}"
    echo "📏 文件大小: $(ls -lah "${PACKAGE_NAME}" | awk '{print $5}')"
    echo ""
    echo "📋 使用说明:"
    echo "1. 将 ${PACKAGE_NAME} 传输到Linux系统"
    echo "2. 解压: tar -xzf '${PACKAGE_NAME}'"
    echo "3. 运行: cd linux-unpacked && ./infinity-notes"
else
    echo "❌ 打包失败!"
    exit 1
fi

cd ..
