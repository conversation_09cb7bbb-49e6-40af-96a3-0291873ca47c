#!/bin/bash

# TipTap 扩展优化修复脚本
# 解决版本不一致和扩展重复问题

echo "🔧 开始 TipTap 扩展优化修复..."

# 1. 统一所有 TipTap 扩展到最新版本
echo "📦 统一扩展版本到 3.0.9..."
npm install \
  @tiptap/react@^3.0.9 \
  @tiptap/starter-kit@^3.0.9 \
  @tiptap/extension-image@^3.0.9 \
  @tiptap/extension-placeholder@^3.0.9 \
  @tiptap/extension-table@^3.0.9 \
  @tiptap/extension-table-row@^3.0.9 \
  @tiptap/extension-table-cell@^3.0.9 \
  @tiptap/extension-table-header@^3.0.9 \
  @tiptap/extension-task-list@^3.0.9 \
  @tiptap/extension-task-item@^3.0.9 \
  @tiptap/static-renderer@^3.0.9

# 2. 移除重复的列表扩展（将使用 StarterKit 内置的）
echo "🗑️ 移除重复的列表扩展..."
npm uninstall \
  @tiptap/extension-bullet-list \
  @tiptap/extension-ordered-list \
  @tiptap/extension-list-item \
  @tiptap/extension-list-keymap

echo "✅ 扩展版本统一完成！"
echo "📝 接下来需要手动修改编辑器配置文件..."
echo ""
echo "需要修改的文件："
echo "- src/components/notes/WysiwygEditor.tsx"
echo ""
echo "主要修改："
echo "1. 移除独立导入的列表扩展"
echo "2. 恢复 StarterKit 中的列表功能" 
echo "3. 可选：重新启用 codeBlock 和 horizontalRule"
