#!/bin/zsh
# 自动将 dist 文件夹内容推送到 infinitynotesweb 仓库 gh-pages 分支

set -e

# 1. 定义变量
DIST_DIR="$(pwd)/dist"
TEMP_DIR="/tmp/infinitynotesweb-deploy"
REPO_URL="https://github.com/duobaobox/infinitynotesweb.git"
BRANCH="gh-pages"

# 2. 清理临时目录
rm -rf "$TEMP_DIR"

# 3. 克隆仓库
git clone "$REPO_URL" "$TEMP_DIR"
cd "$TEMP_DIR"

# 4. 切换到 gh-pages 分支（如无则新建）
git checkout $BRANCH 2>/dev/null || git checkout --orphan $BRANCH

# 5. 清理旧文件（保留 .git 和 .gitignore）
find . -mindepth 1 \( ! -regex ".*/\\.git.*" ! -name ".gitignore" \) -exec rm -rf {} +

# 6. 拷贝 dist 内容
cp -r "$DIST_DIR"/* .

# 7. 提交并推送
if [ -n "$(git status --porcelain)" ]; then
  git add .
  git commit -m "自动部署: $(date '+%Y-%m-%d %H:%M:%S')"
  git push origin $BRANCH --force
else
  echo "没有需要更新的内容。"
fi

echo "部署完成，可在 GitHub Pages 查看最新内容。"
