#!/usr/bin/env node

/**
 * 版本信息注入脚本
 * 在构建时将版本信息注入到应用中
 */

import fs from "fs";
import path from "path";
import { execSync } from "child_process";
import { fileURLToPath } from "url";

// 获取当前文件的目录路径（ES modules 中的 __dirname 替代方案）
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 获取项目根目录
const projectRoot = path.resolve(__dirname, "..");

// 读取 package.json 获取版本信息
const packageJsonPath = path.join(projectRoot, "package.json");
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));

// 获取构建时间
const buildTime = new Date().toISOString();

// 获取 Git 提交哈希
let gitHash = "unknown";
try {
  gitHash = execSync("git rev-parse --short HEAD", {
    encoding: "utf8",
    cwd: projectRoot,
  }).trim();
} catch (error) {
  console.warn("无法获取 Git 提交哈希:", error.message);
}

// 版本信息对象
const versionInfo = {
  version: packageJson.version,
  buildTime,
  gitHash,
  name: packageJson.name,
  description: packageJson.description,
};

console.log("📦 注入版本信息:", versionInfo);

// 更新版本工具文件中的版本号
const versionFilePath = path.join(projectRoot, "src/utils/version.ts");
if (fs.existsSync(versionFilePath)) {
  let versionFileContent = fs.readFileSync(versionFilePath, "utf8");

  // 替换 PACKAGE_VERSION 常量
  versionFileContent = versionFileContent.replace(
    /const PACKAGE_VERSION = "[^"]*";/,
    `const PACKAGE_VERSION = "${versionInfo.version}";`
  );

  fs.writeFileSync(versionFilePath, versionFileContent, "utf8");
  console.log("✅ 已更新版本工具文件");
}

// 创建版本信息文件供构建时使用
const versionInfoPath = path.join(projectRoot, "version-info.json");
fs.writeFileSync(versionInfoPath, JSON.stringify(versionInfo, null, 2), "utf8");
console.log("✅ 已生成版本信息文件:", versionInfoPath);

// 设置环境变量供 Vite 使用
process.env.VITE_APP_VERSION = versionInfo.version;
process.env.VITE_BUILD_TIME = versionInfo.buildTime;
process.env.VITE_GIT_HASH = versionInfo.gitHash;

console.log("✅ 版本信息注入完成");
