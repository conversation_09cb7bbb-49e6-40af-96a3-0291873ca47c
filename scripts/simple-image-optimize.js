#!/usr/bin/env node

/**
 * 简单图片优化脚本
 * 将大图片转换为 WebP 格式或压缩现有格式
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const imagesDir = path.join(__dirname, "../public/images");

/**
 * 获取文件大小（KB）
 */
function getFileSizeKB(filePath) {
  const stats = fs.statSync(filePath);
  return Math.round(stats.size / 1024);
}

/**
 * 手动压缩大图片的建议
 */
function suggestOptimization() {
  console.log("🖼️  图片优化建议:\n");

  const files = fs.readdirSync(imagesDir);
  let totalSize = 0;
  let largeFiles = [];

  files.forEach((file) => {
    const filePath = path.join(imagesDir, file);
    const stats = fs.statSync(filePath);

    if (stats.isFile()) {
      const sizeKB = getFileSizeKB(filePath);
      totalSize += sizeKB;

      if (sizeKB > 500) {
        largeFiles.push({ file, sizeKB });
      }

      console.log(`📄 ${file}: ${sizeKB}KB`);
    }
  });

  console.log(`\n📊 总大小: ${totalSize}KB`);

  if (largeFiles.length > 0) {
    console.log("\n⚠️  需要优化的大文件:");
    largeFiles.forEach(({ file, sizeKB }) => {
      console.log(`   ${file}: ${sizeKB}KB`);
    });

    console.log("\n💡 优化建议:");
    console.log("1. 使用在线工具压缩图片:");
    console.log("   - TinyPNG: https://tinypng.com/");
    console.log("   - Squoosh: https://squoosh.app/");
    console.log("   - Compressor.io: https://compressor.io/");

    console.log("\n2. 或者使用命令行工具:");
    console.log("   # 安装 ImageMagick");
    console.log("   brew install imagemagick  # macOS");
    console.log("   # 然后运行:");
    largeFiles.forEach(({ file }) => {
      const name = path.parse(file).name;
      const ext = path.parse(file).ext;
      if (ext === ".png") {
        console.log(
          `   convert public/images/${file} -quality 80 -resize 1920x1080\\> public/images/${file}`
        );
      } else {
        console.log(
          `   convert public/images/${file} -quality 85 -resize 1920x1080\\> public/images/${file}`
        );
      }
    });

    console.log("\n3. 建议目标大小:");
    console.log("   - 截图/示例图片: < 200KB");
    console.log("   - 图标: < 50KB");
    console.log("   - 背景图: < 500KB");
  } else {
    console.log("\n✅ 所有图片大小都在合理范围内！");
  }
}

// 运行分析
suggestOptimization();
