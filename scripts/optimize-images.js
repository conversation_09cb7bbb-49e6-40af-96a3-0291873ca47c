#!/usr/bin/env node

/**
 * 图片优化脚本
 * 压缩 public/images 目录下的图片文件，减小包体积
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// 图片优化配置
const IMAGE_CONFIG = {
  // PNG 图片质量设置（0-100）
  pngQuality: 80,
  // JPEG 图片质量设置（0-100）
  jpegQuality: 85,
  // 最大宽度限制（像素）
  maxWidth: 1920,
  // 最大高度限制（像素）
  maxHeight: 1080,
  // 目标文件大小上限（KB）
  maxSizeKB: 500
};

const imagesDir = path.join(__dirname, '../public/images');
const backupDir = path.join(__dirname, '../public/images-backup');

/**
 * 检查是否安装了 ImageMagick
 */
function checkImageMagick() {
  try {
    execSync('convert -version', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * 创建备份目录
 */
function createBackup() {
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  const files = fs.readdirSync(imagesDir);
  files.forEach(file => {
    const srcPath = path.join(imagesDir, file);
    const backupPath = path.join(backupDir, file);
    
    if (fs.statSync(srcPath).isFile() && !fs.existsSync(backupPath)) {
      fs.copyFileSync(srcPath, backupPath);
      console.log(`✓ 备份文件: ${file}`);
    }
  });
}

/**
 * 获取文件大小（KB）
 */
function getFileSizeKB(filePath) {
  const stats = fs.statSync(filePath);
  return Math.round(stats.size / 1024);
}

/**
 * 优化单个图片文件
 */
function optimizeImage(filePath) {
  const fileName = path.basename(filePath);
  const fileExt = path.extname(fileName).toLowerCase();
  const originalSizeKB = getFileSizeKB(filePath);
  
  console.log(`\n处理图片: ${fileName} (${originalSizeKB}KB)`);
  
  // 如果文件已经很小，跳过优化
  if (originalSizeKB <= IMAGE_CONFIG.maxSizeKB) {
    console.log(`  ✓ 文件大小已符合要求，跳过优化`);
    return;
  }
  
  const tempPath = filePath + '.tmp';
  let command = '';
  
  try {
    if (fileExt === '.png') {
      // PNG 优化：调整质量和尺寸
      command = `convert "${filePath}" -resize "${IMAGE_CONFIG.maxWidth}x${IMAGE_CONFIG.maxHeight}>" -quality ${IMAGE_CONFIG.pngQuality} "${tempPath}"`;
    } else if (fileExt === '.jpg' || fileExt === '.jpeg') {
      // JPEG 优化：调整质量和尺寸
      command = `convert "${filePath}" -resize "${IMAGE_CONFIG.maxWidth}x${IMAGE_CONFIG.maxHeight}>" -quality ${IMAGE_CONFIG.jpegQuality} "${tempPath}"`;
    } else {
      console.log(`  ⚠ 不支持的文件格式: ${fileExt}`);
      return;
    }
    
    // 执行优化命令
    execSync(command, { stdio: 'ignore' });
    
    const optimizedSizeKB = getFileSizeKB(tempPath);
    const compressionRatio = ((originalSizeKB - optimizedSizeKB) / originalSizeKB * 100).toFixed(1);
    
    // 如果优化后文件更小，则替换原文件
    if (optimizedSizeKB < originalSizeKB) {
      fs.renameSync(tempPath, filePath);
      console.log(`  ✓ 优化完成: ${originalSizeKB}KB → ${optimizedSizeKB}KB (减少 ${compressionRatio}%)`);
    } else {
      fs.unlinkSync(tempPath);
      console.log(`  ⚠ 优化后文件更大，保持原文件`);
    }
    
  } catch (error) {
    console.error(`  ✗ 优化失败: ${error.message}`);
    // 清理临时文件
    if (fs.existsSync(tempPath)) {
      fs.unlinkSync(tempPath);
    }
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🖼️  开始图片优化...\n');
  
  // 检查 ImageMagick
  if (!checkImageMagick()) {
    console.error('❌ 未找到 ImageMagick，请先安装:');
    console.error('   macOS: brew install imagemagick');
    console.error('   Ubuntu: sudo apt-get install imagemagick');
    console.error('   Windows: 下载并安装 https://imagemagick.org/script/download.php#windows');
    process.exit(1);
  }
  
  // 检查图片目录
  if (!fs.existsSync(imagesDir)) {
    console.error(`❌ 图片目录不存在: ${imagesDir}`);
    process.exit(1);
  }
  
  // 创建备份
  console.log('📦 创建备份...');
  createBackup();
  
  // 获取所有图片文件
  const files = fs.readdirSync(imagesDir);
  const imageFiles = files.filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.png', '.jpg', '.jpeg', '.gif', '.webp'].includes(ext);
  });
  
  if (imageFiles.length === 0) {
    console.log('📁 未找到需要优化的图片文件');
    return;
  }
  
  console.log(`\n🎯 找到 ${imageFiles.length} 个图片文件，开始优化...`);
  
  let totalOriginalSize = 0;
  let totalOptimizedSize = 0;
  
  // 优化每个图片文件
  imageFiles.forEach(file => {
    const filePath = path.join(imagesDir, file);
    const originalSize = getFileSizeKB(filePath);
    totalOriginalSize += originalSize;
    
    optimizeImage(filePath);
    
    const optimizedSize = getFileSizeKB(filePath);
    totalOptimizedSize += optimizedSize;
  });
  
  // 显示总结
  const totalSaved = totalOriginalSize - totalOptimizedSize;
  const totalSavedPercent = ((totalSaved / totalOriginalSize) * 100).toFixed(1);
  
  console.log('\n📊 优化完成！');
  console.log(`   原始大小: ${totalOriginalSize}KB`);
  console.log(`   优化后大小: ${totalOptimizedSize}KB`);
  console.log(`   节省空间: ${totalSaved}KB (${totalSavedPercent}%)`);
  console.log(`\n💡 备份文件保存在: ${backupDir}`);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { optimizeImage, IMAGE_CONFIG };
