#!/bin/bash

# Linux构建脚本 - 使用国内镜像

echo "🚀 开始构建Linux版本..."

# 设置镜像环境变量
export ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/
export ELECTRON_CACHE=/tmp/electron-cache
export npm_config_cache=/tmp/npm-cache

# 创建缓存目录
mkdir -p $ELECTRON_CACHE
mkdir -p $npm_config_cache

echo "📦 注入版本信息..."
node scripts/inject-version.js

echo "🔨 构建前端资源..."
npm run build

echo "📱 构建Electron应用..."
# 使用国内镜像下载Electron
ELECTRON_BUILDER_ALLOW_UNRESOLVED_DEPENDENCIES=true \
ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ \
electron-builder --linux

echo "✅ Linux构建完成！"
