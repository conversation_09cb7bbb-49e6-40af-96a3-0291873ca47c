#!/bin/bash

# 小包构建脚本
# 优化应用包体积，目标控制在 100MB 以内

set -e

echo "🚀 开始小包构建..."

# 1. 清理之前的构建
echo "🧹 清理构建目录..."
rm -rf dist dist-electron

# 2. 分析图片大小
echo "📊 分析图片资源..."
node scripts/simple-image-optimize.js

# 3. 检查大图片文件
echo "🔍 检查大文件..."
find public/images -type f -size +500k -exec ls -lh {} \; | while read line; do
    echo "⚠️  发现大文件: $line"
done

# 4. 生成图标
echo "🎨 生成应用图标..."
npm run generate-icons

# 5. 构建前端资源
echo "🏗️  构建前端资源..."
npm run build

# 6. 分析构建产物
echo "📈 分析构建产物大小..."
echo "前端资源大小:"
du -sh dist/
echo ""
echo "主要文件:"
find dist -type f -size +100k -exec ls -lh {} \;

# 7. 构建 Electron 应用
echo "📦 构建 Electron 应用..."
ELECTRON_MIRROR=https://npmmirror.com/mirrors/electron/ npx electron-builder --publish=never --config.compression=maximum

# 8. 分析最终包大小
echo "📊 最终包大小分析:"
if [ -d "dist-electron" ]; then
    echo ""
    echo "=== macOS 版本 ==="
    find dist-electron -name "*.dmg" -exec ls -lh {} \;
    find dist-electron -name "*-mac.zip" -exec ls -lh {} \;
    
    echo ""
    echo "=== Windows 版本 ==="
    find dist-electron -name "*.exe" -exec ls -lh {} \;
    
    echo ""
    echo "=== 应用内容分析 ==="
    if [ -d "dist-electron/mac" ]; then
        echo "macOS 应用大小:"
        du -sh dist-electron/mac/*.app
        echo "应用内容:"
        du -sh dist-electron/mac/*.app/Contents/Resources/app.asar
    fi
fi

# 9. 提供优化建议
echo ""
echo "💡 包体积优化建议:"
echo "1. 如果包仍然过大，考虑:"
echo "   - 压缩 public/images/001.png (当前 2.5MB)"
echo "   - 移除不必要的依赖"
echo "   - 使用 CDN 加载大型资源"
echo ""
echo "2. 进一步优化选项:"
echo "   - 启用 asar 压缩"
echo "   - 移除开发依赖"
echo "   - 优化 Electron 版本"
echo ""
echo "3. 目标包大小:"
echo "   - macOS: < 100MB"
echo "   - Windows: < 100MB"

echo ""
echo "✅ 构建完成！"
