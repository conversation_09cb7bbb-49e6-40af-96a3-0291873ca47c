#!/usr/bin/env node

/**
 * 版本信息测试脚本
 * 验证版本管理系统是否正常工作
 */

import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, "..");

console.log("🧪 测试版本管理系统...\n");

// 1. 检查 package.json 版本
const packageJsonPath = path.join(projectRoot, "package.json");
const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, "utf8"));
console.log("📦 package.json 版本:", packageJson.version);

// 2. 检查版本工具文件
const versionFilePath = path.join(projectRoot, "src/utils/version.ts");
if (fs.existsSync(versionFilePath)) {
  const versionFileContent = fs.readFileSync(versionFilePath, "utf8");
  const versionMatch = versionFileContent.match(/const PACKAGE_VERSION = "([^"]+)";/);
  if (versionMatch) {
    console.log("🔧 版本工具文件版本:", versionMatch[1]);
    
    if (versionMatch[1] === packageJson.version) {
      console.log("✅ 版本工具文件与 package.json 版本一致");
    } else {
      console.log("❌ 版本工具文件与 package.json 版本不一致");
    }
  } else {
    console.log("❌ 无法从版本工具文件中提取版本号");
  }
} else {
  console.log("❌ 版本工具文件不存在");
}

// 3. 检查版本信息文件
const versionInfoPath = path.join(projectRoot, "version-info.json");
if (fs.existsSync(versionInfoPath)) {
  const versionInfo = JSON.parse(fs.readFileSync(versionInfoPath, "utf8"));
  console.log("📄 版本信息文件:", versionInfo);
  
  if (versionInfo.version === packageJson.version) {
    console.log("✅ 版本信息文件与 package.json 版本一致");
  } else {
    console.log("❌ 版本信息文件与 package.json 版本不一致");
  }
} else {
  console.log("⚠️  版本信息文件不存在（需要运行构建脚本生成）");
}

// 4. 检查设置页面是否使用了动态版本
const settingsModalPath = path.join(projectRoot, "src/components/modals/SettingsModal.tsx");
if (fs.existsSync(settingsModalPath)) {
  const settingsContent = fs.readFileSync(settingsModalPath, "utf8");
  
  if (settingsContent.includes("formatVersion(versionInfo")) {
    console.log("✅ 设置页面已使用动态版本显示");
  } else {
    console.log("❌ 设置页面未使用动态版本显示");
  }
  
  if (settingsContent.includes("RC1.0.0") || settingsContent.includes("v 0.1.0")) {
    console.log("⚠️  设置页面仍包含硬编码版本号");
  } else {
    console.log("✅ 设置页面已移除硬编码版本号");
  }
} else {
  console.log("❌ 设置页面文件不存在");
}

console.log("\n🎯 版本管理系统测试完成！");
console.log("\n💡 使用说明:");
console.log("1. 修改版本号：编辑 package.json 中的 version 字段");
console.log("2. 构建应用：运行 npm run build 或 npm run dist");
console.log("3. 版本号会自动同步到所有相关文件和界面显示");
