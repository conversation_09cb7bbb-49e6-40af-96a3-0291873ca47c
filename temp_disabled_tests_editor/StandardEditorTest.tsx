import React, { useState } from "react";
import { Card, Button, Space, Typography, Alert } from "antd";
import StandardEditor from "../components/notes/StandardEditor";

const { Title, Text } = Typography;

/**
 * 标准化编辑器测试组件
 */
const StandardEditorTest: React.FC = () => {
  const [testContent, setTestContent] = useState("");
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  // 测试基本功能
  const testBasicFeatures = () => {
    const content = `# 标准化编辑器功能测试

## 基本文本格式
这是一段普通文本，包含**粗体**、*斜体*和\`代码\`。

## 列表功能
### 无序列表
- 第一项
- 第二项
  - 嵌套项目
  - 另一个嵌套项目
- 第三项

### 有序列表
1. 第一步
2. 第二步
   1. 子步骤 A
   2. 子步骤 B
3. 第三步

### 任务列表
- [ ] 未完成任务
- [x] 已完成任务
- [ ] 另一个未完成任务
  - [ ] 嵌套任务
  - [x] 已完成的嵌套任务

## 表格功能
| 功能 | 状态 | 备注 |
|------|------|------|
| 基础编辑 | ✅ 完成 | 正常工作 |
| 表格支持 | 🔄 测试中 | 当前测试 |
| 任务列表 | ✅ 完成 | 功能正常 |

## 引用
> 这是一个引用块
> 可以包含多行内容

## 代码块
\`\`\`javascript
function hello() {
  console.log("Hello, Tiptap!");
}
\`\`\`

测试完成！`;

    setTestContent(content);
    addTestResult("✅ 基本功能测试内容已加载");
  };

  // 测试表格功能
  const testTableFeatures = () => {
    const content = `# 表格功能测试

## 简单表格
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| A1  | B1  | C1  |
| A2  | B2  | C2  |

## 复杂表格
| 功能名称 | 实现状态 | 测试结果 | 备注说明 |
|----------|----------|----------|----------|
| 表格创建 | ✅ 完成 | 通过 | 支持工具栏创建 |
| 行操作 | ✅ 完成 | 通过 | 增删改行 |
| 列操作 | ✅ 完成 | 通过 | 增删改列 |
| 表格删除 | ✅ 完成 | 通过 | 完整删除表格 |

请在编辑器中选择表格来测试工具栏功能。`;

    setTestContent(content);
    addTestResult("✅ 表格功能测试内容已加载");
  };

  // 测试任务列表功能
  const testTaskListFeatures = () => {
    const content = `# 任务列表功能测试

## 基本任务列表
- [ ] 第一个任务
- [x] 已完成的任务
- [ ] 第三个任务

## 嵌套任务列表
- [ ] 主要任务 1
  - [ ] 子任务 1.1
  - [x] 子任务 1.2（已完成）
  - [ ] 子任务 1.3
- [x] 主要任务 2（已完成）
  - [x] 子任务 2.1（已完成）
  - [x] 子任务 2.2（已完成）
- [ ] 主要任务 3
  - [ ] 子任务 3.1
    - [ ] 深层子任务 3.1.1
    - [ ] 深层子任务 3.1.2

## 混合列表
1. 有序列表项
   - [ ] 任务子项
   - [x] 已完成任务子项
2. 另一个有序列表项
   - 普通无序子项
   - [ ] 任务子项

请在编辑器中选择任务列表来测试工具栏功能。`;

    setTestContent(content);
    addTestResult("✅ 任务列表功能测试内容已加载");
  };

  // 清空内容
  const clearContent = () => {
    setTestContent("");
    addTestResult("🗑️ 内容已清空");
  };

  // 清空测试结果
  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div style={{ padding: "20px", maxWidth: "1200px", margin: "0 auto" }}>
      <Title level={2}>标准化编辑器测试</Title>
      
      <Alert
        message="测试说明"
        description="这是按照 Tiptap 官方标准重写的编辑器组件。请测试各项功能，确保符合官方最佳实践。"
        type="info"
        style={{ marginBottom: 20 }}
      />

      {/* 测试按钮 */}
      <Card title="测试操作" style={{ marginBottom: 20 }}>
        <Space wrap>
          <Button type="primary" onClick={testBasicFeatures}>
            测试基本功能
          </Button>
          <Button onClick={testTableFeatures}>
            测试表格功能
          </Button>
          <Button onClick={testTaskListFeatures}>
            测试任务列表
          </Button>
          <Button danger onClick={clearContent}>
            清空内容
          </Button>
          <Button onClick={clearResults}>
            清空结果
          </Button>
        </Space>
      </Card>

      <div style={{ display: "flex", gap: "20px" }}>
        {/* 左侧：编辑器 */}
        <Card title="标准化编辑器" style={{ flex: 2 }}>
          <div style={{ minHeight: "400px", border: "1px solid #d9d9d9", borderRadius: "6px" }}>
            <StandardEditor
              content={testContent}
              onChange={(content) => {
                setTestContent(content);
                addTestResult("📝 内容已更新");
              }}
              placeholder="开始测试标准化编辑器..."
              onEditorReady={(editor) => {
                addTestResult("🚀 编辑器初始化完成");
              }}
              config={{
                enableTable: true,
                enableTaskList: true,
                enableImage: true,
              }}
            />
          </div>
        </Card>

        {/* 右侧：测试结果和源码 */}
        <div style={{ flex: 1 }}>
          {/* 测试结果 */}
          <Card title="测试结果" size="small" style={{ marginBottom: 20 }}>
            <div style={{ maxHeight: "200px", overflow: "auto" }}>
              {testResults.length === 0 ? (
                <Text type="secondary">暂无测试结果</Text>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} style={{ fontSize: "12px", marginBottom: "4px" }}>
                    {result}
                  </div>
                ))
              )}
            </div>
          </Card>

          {/* Markdown 源码 */}
          <Card title="Markdown 源码" size="small">
            <pre
              style={{
                background: "#f5f5f5",
                padding: "12px",
                borderRadius: "4px",
                fontSize: "11px",
                maxHeight: "300px",
                overflow: "auto",
                whiteSpace: "pre-wrap",
                wordWrap: "break-word",
                margin: 0,
              }}
            >
              {testContent || "暂无内容"}
            </pre>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default StandardEditorTest;
