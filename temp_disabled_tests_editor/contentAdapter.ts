/**
 * 内容格式适配器
 * 处理不同格式的内容转换和兼容性
 */

import type { JSONContent } from "@tiptap/react";
import { ContentConverter, type ConvertResult } from "./contentConverter";
import type { StickyNote } from "../components/types";
import type { DbStickyNote } from "../database";

/**
 * 内容格式版本定义
 */
export const CONTENT_VERSIONS = {
  MARKDOWN_V1: 1, // 原始 Markdown 格式
  PROSEMIRROR_V1: 2, // ProseMirror JSON 格式
} as const;

export type ContentVersion =
  (typeof CONTENT_VERSIONS)[keyof typeof CONTENT_VERSIONS];

/**
 * 内容适配器类
 * 负责处理不同版本和格式的内容转换
 */
export class ContentAdapter {
  private converter: ContentConverter;

  constructor() {
    this.converter = new ContentConverter();
  }

  /**
   * 检测内容格式
   * @param content 内容字符串
   * @returns 内容格式类型
   */
  detectContentFormat(content: string): "markdown" | "prosemirror-json" {
    if (!content || typeof content !== "string") {
      return "markdown";
    }

    const trimmed = content.trim();

    // 检测 ProseMirror JSON 格式
    if (trimmed.startsWith("{") && trimmed.endsWith("}")) {
      try {
        const parsed = JSON.parse(trimmed);
        if (parsed.type === "doc" && parsed.content) {
          return "prosemirror-json";
        }
      } catch {
        // 解析失败，继续检测其他格式
      }
    }

    // 默认为 Markdown
    return "markdown";
  }

  /**
   * 规范化便签内容格式
   * 确保内容格式与元数据一致
   * @param note 便签对象
   * @returns 规范化后的便签
   */
  normalizeNoteContent(note: StickyNote): StickyNote {
    const detectedFormat = this.detectContentFormat(note.content);
    const declaredFormat = note.contentFormat || "markdown";

    // 如果检测到的格式与声明的格式不一致，以检测结果为准并更新元数据
    if (detectedFormat !== declaredFormat) {
      console.warn(
        `Content format mismatch for note ${note.id}: detected ${detectedFormat}, declared ${declaredFormat}`
      );

      return {
        ...note,
        contentFormat: detectedFormat,
        contentVersion:
          detectedFormat === "prosemirror-json"
            ? CONTENT_VERSIONS.PROSEMIRROR_V1
            : CONTENT_VERSIONS.MARKDOWN_V1,
      };
    }

    // 如果没有版本信息，根据格式设置默认版本
    if (!note.contentVersion) {
      return {
        ...note,
        contentVersion:
          declaredFormat === "prosemirror-json"
            ? CONTENT_VERSIONS.PROSEMIRROR_V1
            : CONTENT_VERSIONS.MARKDOWN_V1,
      };
    }

    return note;
  }

  /**
   * 转换便签内容格式
   * @param note 便签对象
   * @param targetFormat 目标格式
   * @returns 转换结果
   */
  convertNoteContent(
    note: StickyNote,
    targetFormat: "markdown" | "prosemirror-json"
  ): ConvertResult & { note?: StickyNote } {
    const currentFormat = note.contentFormat || "markdown";

    // 如果已经是目标格式，直接返回
    if (currentFormat === targetFormat) {
      return {
        success: true,
        content: note.content,
        note,
      };
    }

    let result: ConvertResult;

    // 执行格式转换
    if (currentFormat === "markdown" && targetFormat === "prosemirror-json") {
      // Markdown 转 ProseMirror JSON
      const jsonResult = this.converter.markdownToJSON(note.content);
      if (jsonResult.success) {
        result = {
          ...jsonResult,
          content: JSON.stringify(jsonResult.content), // 转换为字符串存储
        };
      } else {
        result = jsonResult;
      }
    } else if (
      currentFormat === "prosemirror-json" &&
      targetFormat === "markdown"
    ) {
      // ProseMirror JSON 转 Markdown
      try {
        const jsonContent = JSON.parse(note.content) as JSONContent;
        result = this.converter.jsonToMarkdown(jsonContent);
      } catch (error) {
        result = {
          success: false,
          content: "",
          error: `Failed to parse ProseMirror JSON: ${error}`,
        };
      }
    } else {
      result = {
        success: false,
        content: note.content,
        error: `Unsupported conversion: ${currentFormat} to ${targetFormat}`,
      };
    }

    // 如果转换成功，创建新的便签对象
    if (result.success) {
      const updatedNote: StickyNote = {
        ...note,
        content: result.content as string,
        contentFormat: targetFormat,
        contentVersion:
          targetFormat === "prosemirror-json"
            ? CONTENT_VERSIONS.PROSEMIRROR_V1
            : CONTENT_VERSIONS.MARKDOWN_V1,
        updatedAt: new Date(),
      };

      return {
        ...result,
        note: updatedNote,
      };
    }

    return result;
  }

  /**
   * 准备便签内容用于编辑器
   * 确保编辑器接收到正确格式的内容
   * @param note 便签对象
   * @returns 适合编辑器的内容
   */
  prepareContentForEditor(note: StickyNote): string {
    const normalizedNote = this.normalizeNoteContent(note);
    const format = normalizedNote.contentFormat || "markdown";

    if (format === "prosemirror-json") {
      // 如果是 ProseMirror JSON 格式，转换为 Markdown 供编辑器使用
      // 编辑器内部会将 Markdown 转换回 ProseMirror 格式
      try {
        const jsonContent = JSON.parse(normalizedNote.content) as JSONContent;
        const markdownResult = this.converter.jsonToMarkdown(jsonContent);

        if (markdownResult.success) {
          return markdownResult.content as string;
        } else {
          console.warn(
            "Failed to convert ProseMirror to Markdown for editor:",
            markdownResult.error
          );
          return normalizedNote.content; // 降级处理
        }
      } catch (error) {
        console.warn("Failed to parse ProseMirror JSON for editor:", error);
        return normalizedNote.content; // 降级处理
      }
    }

    // Markdown 格式直接使用
    return normalizedNote.content;
  }

  /**
   * 处理编辑器内容变化
   * 将编辑器的输出转换为存储格式
   * @param editorContent 编辑器输出的内容（Markdown）
   * @param note 当前便签对象
   * @param preferredFormat 首选存储格式
   * @returns 更新后的便签内容信息
   */
  processEditorContent(
    editorContent: string,
    _note: StickyNote,
    preferredFormat: "markdown" | "prosemirror-json" = "prosemirror-json"
  ): {
    content: string;
    contentFormat: "markdown" | "prosemirror-json";
    contentVersion: number;
  } {
    // 如果首选格式是 ProseMirror JSON，转换编辑器输出
    if (preferredFormat === "prosemirror-json") {
      const jsonResult = this.converter.markdownToJSON(editorContent);

      if (jsonResult.success) {
        return {
          content: JSON.stringify(jsonResult.content),
          contentFormat: "prosemirror-json",
          contentVersion: CONTENT_VERSIONS.PROSEMIRROR_V1,
        };
      } else {
        console.warn(
          "Failed to convert editor content to ProseMirror JSON, fallback to Markdown:",
          jsonResult.error
        );
        // 降级到 Markdown 存储
        return {
          content: editorContent,
          contentFormat: "markdown",
          contentVersion: CONTENT_VERSIONS.MARKDOWN_V1,
        };
      }
    }

    // 默认使用 Markdown 存储
    return {
      content: editorContent,
      contentFormat: "markdown",
      contentVersion: CONTENT_VERSIONS.MARKDOWN_V1,
    };
  }

  /**
   * 数据库便签转换为前端便签
   * @param dbNote 数据库便签对象
   * @returns 前端便签对象
   */
  dbNoteToFrontendNote(dbNote: DbStickyNote): StickyNote {
    const note: StickyNote = {
      id: dbNote.id,
      x: dbNote.position_x,
      y: dbNote.position_y,
      width: dbNote.width,
      height: dbNote.height,
      content: dbNote.content,
      title: dbNote.title,
      color: dbNote.color as any,
      isEditing: false,
      isTitleEditing: false,
      isNew: false,
      zIndex: dbNote.z_index || 1,
      createdAt: new Date(dbNote.created_at),
      updatedAt: new Date(dbNote.updated_at),
      contentFormat: (dbNote.content_format as any) || "markdown",
      contentVersion: dbNote.content_version || CONTENT_VERSIONS.MARKDOWN_V1,
    };

    // 处理可选字段
    if (dbNote.source_note_ids) {
      try {
        note.sourceNoteIds = JSON.parse(dbNote.source_note_ids);
      } catch (error) {
        console.warn("Failed to parse source note IDs:", error);
      }
    }

    if (dbNote.source_notes_content) {
      try {
        note.sourceNotesContent = JSON.parse(dbNote.source_notes_content);
      } catch (error) {
        console.warn("Failed to parse source notes content:", error);
      }
    }

    if (dbNote.generation_mode) {
      note.generationMode = dbNote.generation_mode as any;
    }

    if (dbNote.thinking_chain) {
      try {
        note.thinkingChain = JSON.parse(dbNote.thinking_chain);
      } catch (error) {
        console.warn("Failed to parse thinking chain:", error);
      }
    }

    if (dbNote.has_thinking !== undefined) {
      note.hasThinking = dbNote.has_thinking;
    }

    return this.normalizeNoteContent(note);
  }

  /**
   * 前端便签转换为数据库便签
   * @param note 前端便签对象
   * @param canvasId 画布ID
   * @returns 数据库便签对象
   */
  frontendNoteToDbNote(
    note: StickyNote,
    canvasId: string
  ): Partial<DbStickyNote> {
    const dbNote: Partial<DbStickyNote> = {
      id: note.id,
      canvas_id: canvasId,
      position_x: note.x,
      position_y: note.y,
      width: note.width,
      height: note.height,
      content: note.content,
      title: note.title,
      color: note.color,
      z_index: note.zIndex,
      content_format: note.contentFormat || "markdown",
      content_version: note.contentVersion || CONTENT_VERSIONS.MARKDOWN_V1,
      updated_at: new Date().toISOString(),
    };

    // 处理可选字段
    if (note.sourceNoteIds && note.sourceNoteIds.length > 0) {
      dbNote.source_note_ids = JSON.stringify(note.sourceNoteIds);
    }

    if (note.sourceNotesContent && note.sourceNotesContent.length > 0) {
      dbNote.source_notes_content = JSON.stringify(note.sourceNotesContent);
    }

    if (note.generationMode) {
      dbNote.generation_mode = note.generationMode;
    }

    if (note.thinkingChain) {
      dbNote.thinking_chain = JSON.stringify(note.thinkingChain);
    }

    if (note.hasThinking !== undefined) {
      dbNote.has_thinking = note.hasThinking;
    }

    return dbNote;
  }
}

/**
 * 默认内容适配器实例
 */
export const defaultContentAdapter = new ContentAdapter();

/**
 * 便捷函数
 */
export const contentAdapter = {
  /**
   * 准备便签内容用于编辑器
   */
  prepareForEditor: (note: StickyNote) =>
    defaultContentAdapter.prepareContentForEditor(note),

  /**
   * 处理编辑器内容变化
   */
  processEditorContent: (
    editorContent: string,
    note: StickyNote,
    preferredFormat?: "markdown" | "prosemirror-json"
  ) =>
    defaultContentAdapter.processEditorContent(
      editorContent,
      note,
      preferredFormat
    ),

  /**
   * 转换便签内容格式
   */
  convertNote: (
    note: StickyNote,
    targetFormat: "markdown" | "prosemirror-json"
  ) => defaultContentAdapter.convertNoteContent(note, targetFormat),

  /**
   * 数据库便签转换为前端便签
   */
  fromDb: (dbNote: DbStickyNote) =>
    defaultContentAdapter.dbNoteToFrontendNote(dbNote),

  /**
   * 前端便签转换为数据库便签
   */
  toDb: (note: StickyNote, canvasId: string) =>
    defaultContentAdapter.frontendNoteToDbNote(note, canvasId),
};
