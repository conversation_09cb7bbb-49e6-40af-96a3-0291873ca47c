# 安全策略

## 支持的版本

我们目前支持以下版本的安全更新：

| 版本 | 支持状态 |
| --- | --- |
| 0.1.x | :white_check_mark: |

## 报告漏洞

我们非常重视安全问题。如果您发现了安全漏洞，请负责任地向我们披露。

### 如何报告

1. **不要**在公共issue中报告安全漏洞
2. 发送邮件到项目维护者（通过GitHub私信）
3. 包含以下信息：
   - 漏洞的详细描述
   - 重现步骤
   - 潜在的影响
   - 建议的修复方案（如果有）

### 响应时间

- **确认收到**: 48小时内
- **初步评估**: 7天内
- **详细响应**: 30天内

### 安全更新流程

1. 我们会验证和重现报告的问题
2. 评估安全影响和严重程度
3. 开发和测试修复方案
4. 发布安全更新
5. 公开披露（在修复发布后）

## 安全最佳实践

### 用户数据保护

- **本地存储**: 所有用户数据存储在本地浏览器中
- **数据加密**: 敏感配置信息进行加密存储
- **隐私保护**: 不收集或传输用户的便签内容
- **API密钥**: AI服务的API密钥安全存储

### 代码安全

- **依赖管理**: 定期更新依赖包，修复已知漏洞
- **输入验证**: 对所有用户输入进行验证和清理
- **XSS防护**: 防止跨站脚本攻击
- **CSRF防护**: 防止跨站请求伪造

### 浏览器安全

- **内容安全策略**: 实施CSP头部
- **安全头部**: 设置适当的安全HTTP头部
- **同源策略**: 遵循浏览器同源策略
- **安全上下文**: 在HTTPS环境中运行

## 安全配置建议

### 部署安全

```nginx
# Nginx安全配置示例
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### 开发环境

- 使用HTTPS进行开发
- 定期更新开发依赖
- 启用安全linting规则
- 进行安全代码审查

## 已知安全考虑

### AI服务集成

- API密钥存储在本地，不会泄露到服务器
- 用户可以选择是否使用AI功能
- AI请求内容可以由用户控制
- 支持本地AI模型，避免数据外传

### 数据存储

- 使用浏览器的IndexedDB进行本地存储
- 不依赖外部数据库或云服务
- 用户完全控制自己的数据
- 支持数据导出和备份

### 第三方依赖

- 定期审计npm依赖包
- 使用npm audit检查漏洞
- 及时更新有安全问题的包
- 最小化依赖数量

## 安全更新通知

安全更新将通过以下渠道发布：

- GitHub Releases页面
- 项目README文件
- CHANGELOG.md文件

## 联系信息

如果您有安全相关的问题或建议，请通过以下方式联系我们：

- GitHub Issues（非敏感问题）
- GitHub私信（敏感安全问题）

## 致谢

我们感谢安全研究人员和社区成员帮助保持项目的安全性。负责任地披露安全问题的贡献者将在修复发布后得到适当的致谢。

## 免责声明

虽然我们努力确保软件的安全性，但不能保证完全没有漏洞。用户应该：

- 定期备份重要数据
- 在受信任的环境中使用软件
- 及时更新到最新版本
- 遵循安全最佳实践

本安全策略可能会随时更新，请定期查看最新版本。
