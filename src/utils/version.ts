/**
 * 版本信息管理工具
 * 统一管理应用版本信息，支持 Electron 和 Web 环境
 */

// 从 package.json 导入版本信息（在构建时会被替换为实际版本号）
const PACKAGE_VERSION = "1.0.0";

/**
 * 版本信息接口
 */
export interface VersionInfo {
  /** 应用版本号 */
  version: string;
  /** 构建时间 */
  buildTime?: string;
  /** Git 提交哈希 */
  gitHash?: string;
  /** 环境类型 */
  environment: "electron" | "web";
}

/**
 * 获取应用版本信息
 * 优先从 Electron API 获取，如果不可用则使用 package.json 中的版本
 */
export async function getVersionInfo(): Promise<VersionInfo> {
  let version = PACKAGE_VERSION;
  let environment: "electron" | "web" = "web";

  // 检查是否在 Electron 环境中
  if (window.electronAPI?.getVersion) {
    try {
      // 从 Electron 主进程获取版本信息
      version = await window.electronAPI.getVersion();
      environment = "electron";
    } catch (error) {
      console.warn("无法从 Electron API 获取版本信息，使用默认版本:", error);
    }
  }

  return {
    version,
    environment,
    buildTime: process.env.BUILD_TIME,
    gitHash: process.env.GIT_HASH,
  };
}

/**
 * 获取简单的版本字符串
 * 同步方法，适用于不需要异步获取的场景
 */
export function getVersionSync(): string {
  return PACKAGE_VERSION;
}

/**
 * 格式化版本显示
 * @param versionInfo 版本信息对象
 * @param format 显示格式
 */
export function formatVersion(
  versionInfo: VersionInfo,
  format: "simple" | "detailed" = "simple"
): string {
  if (format === "simple") {
    return `v${versionInfo.version}`;
  }

  let result = `v${versionInfo.version}`;
  
  if (versionInfo.environment === "electron") {
    result += " (桌面版)";
  } else {
    result += " (网页版)";
  }

  if (versionInfo.buildTime) {
    result += ` - 构建于 ${versionInfo.buildTime}`;
  }

  if (versionInfo.gitHash) {
    result += ` (${versionInfo.gitHash.substring(0, 7)})`;
  }

  return result;
}

/**
 * 检查版本是否为开发版本
 */
export function isDevelopmentVersion(version: string): boolean {
  return version.includes("dev") || 
         version.includes("alpha") || 
         version.includes("beta") || 
         version.includes("rc") ||
         version.includes("RC");
}

/**
 * 比较版本号
 * @param version1 版本号1
 * @param version2 版本号2
 * @returns 1: version1 > version2, 0: 相等, -1: version1 < version2
 */
export function compareVersions(version1: string, version2: string): number {
  // 移除 v 前缀和后缀标识符
  const clean1 = version1.replace(/^v/, "").split(/[-+]/)[0];
  const clean2 = version2.replace(/^v/, "").split(/[-+]/)[0];

  const parts1 = clean1.split(".").map(Number);
  const parts2 = clean2.split(".").map(Number);

  const maxLength = Math.max(parts1.length, parts2.length);

  for (let i = 0; i < maxLength; i++) {
    const part1 = parts1[i] || 0;
    const part2 = parts2[i] || 0;

    if (part1 > part2) return 1;
    if (part1 < part2) return -1;
  }

  return 0;
}

// 导出默认版本号，用于需要同步获取版本的场景
export const VERSION = PACKAGE_VERSION;
