/* 导入全局磨砂效果变量 */
@import "./styles/glass-effects.css";

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: #f5f5f5;
}

/* Global Custom Scrollbar Styles - 默认显示滚动条 */
*::-webkit-scrollbar {
  width: 17px;
  height: 17px;
}

*::-webkit-scrollbar-track {
  background: transparent;
}

*::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.15); /* 默认显示滚动条 */
  border-radius: 4px;
  border: 4px solid transparent;
  background-clip: content-box;
}

*:hover::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.25); /* 悬浮时加深颜色 */
}

/* Firefox scrollbar styles - 默认显示滚动条 */
* {
  scrollbar-width: auto; /* Or 'thin' if you prefer that as a default */
  scrollbar-color: rgba(0, 0, 0, 0.15) transparent; /* 默认显示滚动条 */
}

*:hover {
  scrollbar-color: rgba(0, 0, 0, 0.25) transparent; /* 悬浮时加深颜色 */
}
