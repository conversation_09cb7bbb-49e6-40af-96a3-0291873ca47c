/* 侧边栏半透明磨砂效果样式 */

/*
 * 跨平台兼容的半透明磨砂效果实现
 * - 使用 backdrop-filter 实现现代浏览器的磨砂效果
 * - 为不支持的浏览器提供渐变降级方案
 * - 最小性能开销，GPU 硬件加速
 */

/* 磨砂效果变量已移至全局 src/styles/glass-effects.css */

.sidebar {
  /* 基础布局 */
  position: fixed;
  top: 0;
  width: 200px; /* 调整侧边栏宽度为200px */
  height: 100vh;
  z-index: 1000;
  overflow: hidden;
  transition: left 0.3s ease;

  /* 磨砂效果 */
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-right: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
}

/* 浏览器降级方案 - 合并重复的 @supports 查询 */
@supports not (backdrop-filter: blur(12px)) {
  .sidebar {
    background: var(--glass-fallback);
  }
}

/* 侧边栏触发按钮样式 */
.sidebar-toggle {
  position: fixed;
  top: 16px;
  z-index: 1001;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border-left: none;
  border-radius: 0 8px 8px 0;

  /* 继承侧边栏的磨砂效果 */
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
}

/* 触发按钮悬停效果 */
.sidebar-toggle:hover {
  background: rgba(248, 249, 250, 0.9);
  border-color: rgba(22, 119, 255, 0.8);
  backdrop-filter: blur(16px) saturate(200%);
  -webkit-backdrop-filter: blur(16px) saturate(200%);
}

/* 便签列表项磨砂效果 - 保持原有设计，优化结构 */
.note-list-item {
  background: rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(0, 0, 0, 0.05) !important; /* 强制应用边框，确保所有便签都有统一的边框 */
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
  transition: all 0.2s ease;
}

.note-list-item:hover {
  background: rgba(24, 144, 255, 0.08);
  border-color: rgba(
    24,
    144,
    255,
    0.3
  ) !important; /* 确保悬停时边框颜色也能正确应用 */
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

/* 确保最后一个便签项也有完整的边框 */
.note-list-item:last-child {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* 浏览器降级方案 - 统一处理不支持 backdrop-filter 的情况 */
@supports not (backdrop-filter: blur(4px)) {
  .sidebar,
  .sidebar-toggle {
    background: var(--glass-fallback);
  }

  .note-list-item {
    background: rgba(255, 255, 255, 0.8);
  }

  .note-list-item:hover {
    background: rgba(24, 144, 255, 0.12);
  }
}

/* 深色模式磨砂效果变量已移至全局 src/styles/glass-effects.css */
@media (prefers-color-scheme: dark) {
  .note-list-item {
    background: rgba(40, 40, 40, 0.6);
    border-color: rgba(255, 255, 255, 0.05) !important;
  }

  .note-list-item:hover {
    background: rgba(24, 144, 255, 0.15);
    border-color: rgba(24, 144, 255, 0.4) !important;
  }

  /* 深色模式下确保最后一个便签项也有完整的边框 */
  .note-list-item:last-child {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  }
}

/* 用户卡片样式 */
.user-card {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.user-card:hover {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 侧边栏按钮图标和文字间距优化 */
.sidebar .ant-btn {
  /* 为带图标和文字的按钮设置优雅间距 */
  gap: 8px; /* 图标和文字之间的间距 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 带文字的按钮样式优化 */
.sidebar .ant-btn:not(.ant-btn-icon-only) {
  gap: 8px; /* 图标和文字间距 */
  padding-left: 12px;
  padding-right: 12px;
}

/* 只有图标的按钮保持紧凑 */
.sidebar .ant-btn.ant-btn-icon-only {
  gap: 0;
  padding: 0;
}

/* 新建画布按钮特殊样式 */
.sidebar .create-canvas-btn {
  gap: 6px; /* 稍微紧凑一点的间距，适合较长的文字 */
}

/* 新建画布按钮图标样式 */
.sidebar .create-canvas-btn .anticon {
  font-size: 14px; /* 图标大小 */
  margin-right: 2px; /* 额外的右边距，增强视觉分离 */
}

/* 设置按钮图标大小优化 */
.sidebar .settings-btn .anticon {
  font-size: 13px; /* 稍微增大图标尺寸，提升视觉平衡 */
}

/* 通用图标样式优化 */
.sidebar .ant-btn .anticon {
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1); /* 更流畅的过渡动画 */
  line-height: 1; /* 确保图标垂直居中 */
}

/* 按钮悬停时图标的微妙动效 */
.sidebar .ant-btn:hover .anticon {
  transform: scale(1.05); /* 轻微放大图标 */
}

/* 按钮文字样式优化 */
.sidebar .ant-btn .ant-btn-content {
  display: flex;
  align-items: center;
  gap: inherit; /* 继承父元素的gap设置 */
}

/* 确保按钮内容垂直居中 */
.sidebar .ant-btn > span {
  display: flex;
  align-items: center;
  gap: inherit;
}

/* 画布列表项图标和文字间距优化 */
.sidebar .ant-list-item {
  /* 确保列表项内容对齐 */
  display: flex;
  align-items: center;
}

/* 画布列表项Avatar图标优化 */
.sidebar .ant-avatar {
  /* 确保Avatar图标居中对齐 */
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* 画布列表项文字容器优化 */
.sidebar .ant-list-item .ant-avatar + div {
  /* Avatar后面的文字容器 */
  margin-left: 12px; /* 与Avatar的marginRight保持一致 */
  flex: 1;
  min-width: 0; /* 允许文字容器收缩 */
}

/* 便签列表项颜色条和文字间距优化 */
.sidebar .note-list-item .ant-typography {
  /* 便签标题文字样式 */
  margin-left: 8px; /* 与颜色条的marginRight保持一致 */
}

/* ========== Ant Design Tabs 图标和文字间距优化 ========== */

/* 通用tabs标签图标和文字间距 */
.ant-tabs-tab .ant-tabs-tab-btn span {
  display: flex;
  align-items: center;
  gap: 6px; /* 图标和文字之间的优雅间距 */
}

/* 设置模态框中的tabs特殊优化 */
.settings-modal .ant-tabs-tab .ant-tabs-tab-btn span {
  gap: 8px; /* 设置页面中稍大的间距，提升可读性 */
  font-size: 14px; /* 统一字体大小 */
  font-weight: 500; /* 适中的字重 */
}

/* tabs图标样式优化 */
.ant-tabs-tab .ant-tabs-tab-btn span .anticon {
  font-size: 14px; /* 图标大小 */
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* 设置模态框tabs图标特殊样式 */
.settings-modal .ant-tabs-tab .ant-tabs-tab-btn span .anticon {
  font-size: 15px; /* 设置页面中稍大的图标 */
}

/* tabs悬停效果优化 */
.ant-tabs-tab:hover .ant-tabs-tab-btn span .anticon {
  transform: scale(1.05); /* 轻微放大图标 */
}

/* 激活状态的tabs样式 */
.ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn span .anticon {
  transform: scale(1.1); /* 激活时图标稍大 */
}

/* 左侧tabs特殊优化（如设置模态框） */
.ant-tabs-left .ant-tabs-tab .ant-tabs-tab-btn span {
  justify-content: flex-start; /* 左对齐 */
  padding: 4px 0; /* 增加垂直内边距 */
}

/* 确保tabs文字不换行 */
.ant-tabs-tab .ant-tabs-tab-btn span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 现代化搜索输入框样式 */
.notes-search-input {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.notes-search-input:hover {
  border-color: rgba(24, 144, 255, 0.3);
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
}

.notes-search-input:focus,
.notes-search-input.ant-input-focused {
  border-color: #1677ff;
  box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.08), 0 2px 8px rgba(0, 0, 0, 0.08);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* 搜索高亮样式 */
.search-highlight {
  background-color: #fff3cd;
  color: #856404;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 500;
}

/* 深色模式样式 */
@media (prefers-color-scheme: dark) {
  /* 用户卡片 - 深色模式 */
  .user-card {
    background: linear-gradient(
      135deg,
      rgba(30, 41, 59, 0.8) 0%,
      rgba(15, 23, 42, 0.9) 100%
    ) !important;
    border-color: rgba(255, 255, 255, 0.08) !important;
  }

  .user-card:hover {
    background: linear-gradient(
      135deg,
      rgba(51, 65, 85, 0.9) 0%,
      rgba(30, 41, 59, 0.95) 100%
    ) !important;
    border-color: rgba(22, 119, 255, 0.3) !important;
  }

  /* 搜索输入框 - 深色模式 */
  .notes-search-input {
    background: rgba(40, 40, 40, 0.9);
    border-color: rgba(255, 255, 255, 0.08);
    color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }

  .notes-search-input:hover {
    border-color: rgba(24, 144, 255, 0.4);
    background: rgba(45, 45, 45, 0.95);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  .notes-search-input:focus,
  .notes-search-input.ant-input-focused {
    border-color: #1677ff;
    box-shadow: 0 0 0 3px rgba(22, 119, 255, 0.15), 0 2px 8px rgba(0, 0, 0, 0.3);
    background: rgba(50, 50, 50, 0.98);
  }

  /* 搜索高亮 - 深色模式 */
  .search-highlight {
    background-color: rgba(255, 193, 7, 0.25);
    color: #ffc107;
    border-radius: 3px;
  }

  /* 便签列表项 - 深色模式 */
  .note-list-item {
    background: rgba(40, 40, 40, 0.6);
    border-color: rgba(255, 255, 255, 0.05);
  }

  .note-list-item:hover {
    background: rgba(24, 144, 255, 0.15);
    border-color: rgba(24, 144, 255, 0.4);
  }

  .note-list-item:last-child {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05) !important;
  }

  /* 设置按钮区域 - 深色模式 */
  .sidebar-settings-area {
    background: rgba(30, 41, 59, 0.8) !important;
    border-top-color: rgba(255, 255, 255, 0.08) !important;
  }
}

/* 设置按钮区域样式 */
.sidebar-settings-area {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
}

/* 浏览器降级方案 - 设置按钮区域 */
@supports not (backdrop-filter: blur(12px)) {
  .sidebar-settings-area {
    background: var(--glass-fallback);
  }
}

/* 微交互动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 搜索结果高亮动画 */
.search-highlight {
  animation: pulse 1.5s ease-in-out;
}

/* 滚动条样式优化 - 默认显示 */
.ant-list::-webkit-scrollbar {
  width: 4px;
}

.ant-list::-webkit-scrollbar-track {
  background: transparent;
}

.ant-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15); /* 默认显示滚动条 */
  border-radius: 2px;
}

.ant-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.25); /* 悬浮时加深颜色 */
}

/* 性能优化 - 减少动画对低性能设备的影响 */
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .sidebar-toggle,
  .note-list-item,
  .notes-search-input,
  .search-highlight,
  .user-card {
    transition: none !important;
    animation: none !important;
  }
}
