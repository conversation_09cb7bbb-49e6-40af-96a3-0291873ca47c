/* 设置弹窗 - 遵循 Ant Design 标准规范 */

/*
 * 针对Electron环境的样式修复
 * 确保设置弹窗在所有环境中都有正确的灰色背景
 */

/* 全局设置弹窗样式 - 最高优先级 */
.ant-modal-wrap .settings-modal .ant-modal-content,
.ant-modal-root .settings-modal .ant-modal-content,
div[class*="ant-modal"] .settings-modal .ant-modal-content {
  background-color: #f5f5f5 !important;
}

/* 设置弹窗背景色 - 使用常见的设置页面灰色 */
/* 使用更通用的选择器，确保在所有环境中都能正确应用 */
.settings-modal .ant-modal-content {
  position: relative;
  background-color: #f5f5f5 !important; /* 设置页面常见的浅灰色背景 */
  background-clip: padding-box;
  border: 0;
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08),
    0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: auto;
  padding: 20px 24px;
}

/* 备用选择器，确保在不同Ant Design版本中都能生效 */
.ant-modal.settings-modal .ant-modal-content {
  background-color: #f5f5f5 !important;
}

/* 设置弹窗标题头部背景透明 */
.settings-modal .ant-modal-header {
  background: transparent !important; /* 设置标题头部背景为透明 */
  border-bottom: none !important; /* 移除底部边框 */
  padding: 0 0 16px 0 !important; /* 调整内边距 */
  color: rgba(0, 0, 0, 0.88);
  border-radius: 8px 8px 0 0;
}

/* 备用选择器，确保标题样式在所有环境中生效 */
.ant-modal.settings-modal .ant-modal-header {
  background: transparent !important;
  border-bottom: none !important;
}

.settings-modal .ant-tabs {
  height: 100%;
}

.settings-modal .ant-tabs-tab-list {
  height: 100%;
  width: 160px;
  margin: 0;
}

.settings-modal .ant-tabs-content-holder {
  height: 100%;
  overflow: auto;
  padding: 0 24px;
}

.settings-modal .ant-tabs-tabpane {
  height: 100%;
}

.settings-modal .ant-tabs-content {
  height: 100%;
}

/* 内容区域 */
.settings-modal-content {
  height: 100%;
  overflow-y: auto;
}

/* 表单操作按钮区域 */
.settings-modal .form-actions {
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
  margin-top: 16px;
}

/* ========== 统一卡片标题样式规范 ========== */
/*
 * 用于统一所有设置页面中的卡片标题样式
 * 确保视觉一致性和易于维护
 */

/* 主要卡片标题样式 */
.card-section-title {
  margin: 0 0 16px 0 !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #262626 !important;
  line-height: 1.4 !important;
  display: flex !important;
  align-items: center !important;
}

/* 卡片标题中的图标样式 */
.card-section-title .anticon {
  font-size: 14px;
  color: #1890ff;
  margin-right: 8px;
  flex-shrink: 0; /* 防止图标被压缩 */
}

/* ========== 设置模态框 Tabs 图标和文字间距优化 ========== */

/* 设置模态框tabs标签优化 */
.settings-modal .ant-tabs-tab {
  padding: 12px 16px !important; /* 增加内边距，提升点击体验 */
  margin: 0 0 4px 0 !important; /* 标签间距 */
  border-radius: 6px 0 0 6px !important; /* 左侧圆角 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  /* 预留右侧边框空间，防止激活时位移 */
  border-right: 3px solid transparent !important;
  position: relative;
}

/* 设置模态框tabs标签按钮内容 */
.settings-modal .ant-tabs-tab .ant-tabs-tab-btn {
  padding: 0 !important;
  width: 100%;
  text-align: left;
}

/* 设置模态框tabs标签文字和图标容器 */
.settings-modal .ant-tabs-tab .ant-tabs-tab-btn span {
  display: flex !important;
  align-items: center !important;
  gap: 10px !important; /* 图标和文字间距 */
  font-size: 14px !important;
  font-weight: 500 !important;
  color: #595959 !important;
  width: 100%;
  justify-content: flex-start;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important; /* 添加文字颜色过渡 */
}

/* 设置模态框tabs图标样式 */
.settings-modal .ant-tabs-tab .ant-tabs-tab-btn span .anticon {
  font-size: 16px !important; /* 稍大的图标 */
  color: #8c8c8c !important; /* 默认图标颜色 */
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  flex-shrink: 0 !important;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 设置模态框tabs悬停效果 */
.settings-modal .ant-tabs-tab:hover:not(.ant-tabs-tab-active) {
  background-color: rgba(0, 0, 0, 0.04) !important;
  border-right: 3px solid rgba(24, 144, 255, 0.3) !important;
}

.settings-modal
  .ant-tabs-tab:hover:not(.ant-tabs-tab-active)
  .ant-tabs-tab-btn
  span {
  color: #262626 !important;
}

.settings-modal
  .ant-tabs-tab:hover:not(.ant-tabs-tab-active)
  .ant-tabs-tab-btn
  span
  .anticon {
  color: #1890ff !important;
  transform: scale(1.05);
}

/* 设置模态框tabs激活状态 */
.settings-modal .ant-tabs-tab.ant-tabs-tab-active {
  background-color: #ffffff !important;
  border-right: 3px solid #1890ff !important;
}

.settings-modal .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn span {
  color: #1890ff !important;
  font-weight: 600 !important;
}

.settings-modal
  .ant-tabs-tab.ant-tabs-tab-active
  .ant-tabs-tab-btn
  span
  .anticon {
  color: #1890ff !important;
  transform: scale(1.1);
}

/* 特殊颜色的图标 */
.card-section-title .anticon.icon-success {
  color: #52c41a;
}

.card-section-title .anticon.icon-warning {
  color: #fa8c16;
}

.card-section-title .anticon.icon-danger {
  color: #ff4d4f;
}

.card-section-title .anticon.icon-purple {
  color: #722ed1;
}

/* 紧凑模式的卡片标题 */
.card-section-title.compact {
  margin: 0 0 12px 0 !important;
  font-size: 13px !important;
}

.card-section-title.compact .anticon {
  font-size: 13px;
  margin-right: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-section-title {
    margin: 0 0 12px 0 !important;
    font-size: 13px !important;
  }

  .card-section-title .anticon {
    font-size: 13px;
    margin-right: 6px;
  }
}

@media (max-width: 576px) {
  .card-section-title {
    font-size: 12px !important;
  }

  .card-section-title .anticon {
    font-size: 12px;
    margin-right: 4px;
  }
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .card-section-title {
    color: #f0f0f0 !important;
  }

  .card-section-title .anticon {
    color: #69c0ff;
  }

  .card-section-title .anticon.icon-success {
    color: #73d13d;
  }

  .card-section-title .anticon.icon-warning {
    color: #ffc53d;
  }

  .card-section-title .anticon.icon-danger {
    color: #ff7875;
  }

  .card-section-title .anticon.icon-purple {
    color: #b37feb;
  }
}
