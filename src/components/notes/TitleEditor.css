/**
 * 标题编辑器样式
 * 专门用于单行标题编辑的样式定义
 */

/* 标题编辑器容器 */
.title-editor-container {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 32px;
  position: relative;
}

/* 标题编辑器内容区域 */
.title-editor-content {
  flex: 1;
  width: 100%;
}

/* 标题编辑器的 ProseMirror 样式 */
.title-editor-content .ProseMirror {
  outline: none;
  border: none;
  padding: 2px 8px;
  font-size: inherit;
  font-weight: bold;
  font-family: inherit;
  line-height: 1.4;
  color: inherit;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  min-height: 28px;
  
  /* 单行编辑样式 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  
  /* 禁用换行 */
  word-break: keep-all;
  word-wrap: normal;
}

/* 聚焦状态 */
.title-editor-content .ProseMirror:focus {
  background: rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 占位符样式 */
.title-editor-content .ProseMirror.is-editor-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #aaa;
  pointer-events: none;
  height: 0;
}

/* 空节点样式 */
.title-editor-content .ProseMirror .is-empty::before {
  content: attr(data-placeholder);
  float: left;
  color: #aaa;
  pointer-events: none;
  height: 0;
}

/* 基础文本格式化样式 */
.title-editor-content .ProseMirror strong {
  font-weight: bolder;
}

.title-editor-content .ProseMirror em {
  font-style: italic;
}

.title-editor-content .ProseMirror s {
  text-decoration: line-through;
}

.title-editor-content .ProseMirror code {
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  padding: 1px 4px;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.9em;
}

/* 选中文本样式 */
.title-editor-content .ProseMirror ::selection {
  background: rgba(24, 144, 255, 0.3);
}

/* 加载状态样式 */
.title-editor-loading {
  display: flex;
  align-items: center;
  padding: 2px 8px;
  font-size: inherit;
  font-weight: bold;
  color: #aaa;
  background: rgba(0, 0, 0, 0.06);
  border-radius: 4px;
  min-height: 28px;
}

/* 禁用状态 */
.title-editor-container.disabled .ProseMirror {
  color: #6c757d;
  cursor: default;
  background: rgba(0, 0, 0, 0.03);
}

.title-editor-container.disabled .ProseMirror * {
  pointer-events: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .title-editor-content .ProseMirror {
    padding: 1px 6px;
    font-size: 14px;
    min-height: 26px;
  }
  
  .title-editor-loading {
    padding: 1px 6px;
    font-size: 14px;
    min-height: 26px;
  }
}

/* 确保在便签中的样式兼容性 */
.sticky-note .title-editor-container {
  margin-right: 8px;
  flex: 0 0 auto;
  min-width: 60px;
  max-width: calc(100% - 40px);
}

.sticky-note .title-editor-content .ProseMirror {
  box-sizing: border-box;
}
