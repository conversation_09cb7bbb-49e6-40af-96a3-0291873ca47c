/**
 * 基础工具栏组件
 * 严格按照 Tiptap 官方文档实现，只包含基本的格式化功能
 */

import React from "react";

/**
 * 工具栏属性接口
 */
interface BasicToolbarProps {
  /** 编辑器实例 */
  editor: any;
  /** 自定义类名 */
  className?: string;
}

/**
 * 基础工具栏组件
 * 提供常用的格式化按钮
 */
const BasicToolbar: React.FC<BasicToolbarProps> = ({
  editor,
  className = "",
}) => {
  // 如果编辑器未初始化，不显示工具栏
  if (!editor) {
    return null;
  }

  return (
    <div className={`basic-toolbar ${className}`}>
      {/* 基础格式化按钮 */}
      <div className="toolbar-group">
        <button
          onClick={() => editor.chain().focus().toggleBold().run()}
          disabled={!editor.can().chain().focus().toggleBold().run()}
          className={editor.isActive("bold") ? "is-active" : ""}
          title="粗体 (Ctrl+B)"
        >
          <strong>B</strong>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleItalic().run()}
          disabled={!editor.can().chain().focus().toggleItalic().run()}
          className={editor.isActive("italic") ? "is-active" : ""}
          title="斜体 (Ctrl+I)"
        >
          <em>I</em>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleStrike().run()}
          disabled={!editor.can().chain().focus().toggleStrike().run()}
          className={editor.isActive("strike") ? "is-active" : ""}
          title="删除线"
        >
          <s>S</s>
        </button>

        <button
          onClick={() => editor.chain().focus().toggleCode().run()}
          disabled={!editor.can().chain().focus().toggleCode().run()}
          className={editor.isActive("code") ? "is-active" : ""}
          title="行内代码"
        >
          &lt;/&gt;
        </button>
      </div>

      {/* 分隔符 */}
      <div className="toolbar-divider"></div>

      {/* 列表按钮 */}
      <div className="toolbar-group">
        <button
          onClick={() => editor.chain().focus().toggleBulletList().run()}
          className={editor.isActive("bulletList") ? "is-active" : ""}
          title="无序列表"
        >
          •
        </button>

        <button
          onClick={() => editor.chain().focus().toggleOrderedList().run()}
          className={editor.isActive("orderedList") ? "is-active" : ""}
          title="有序列表"
        >
          1.
        </button>

        <button
          onClick={() => editor.chain().focus().toggleTaskList().run()}
          className={editor.isActive("taskList") ? "is-active" : ""}
          title="任务列表"
        >
          ☐
        </button>
      </div>

      {/* 分隔符 */}
      <div className="toolbar-divider"></div>

      {/* 表格按钮 */}
      <div className="toolbar-group">
        <button
          onClick={() => {
            if (editor.isActive("table")) {
              editor.chain().focus().deleteTable().run();
            } else {
              editor
                .chain()
                .focus()
                .insertTable({
                  rows: 3,
                  cols: 3,
                  withHeaderRow: true,
                })
                .run();
            }
          }}
          className={editor.isActive("table") ? "is-active" : ""}
          title={editor.isActive("table") ? "删除表格" : "插入表格 (3x3)"}
        >
          ⊞
        </button>

        {editor.isActive("table") && (
          <>
            <button
              onClick={() => editor.chain().focus().addColumnAfter().run()}
              disabled={!editor.can().addColumnAfter()}
              title="添加列"
            >
              +│
            </button>

            <button
              onClick={() => editor.chain().focus().addRowAfter().run()}
              disabled={!editor.can().addRowAfter()}
              title="添加行"
            >
              +─
            </button>
          </>
        )}
      </div>

      {/* 标题功能已移除，用户可以通过 Markdown 语法使用：# 标题1  ## 标题2  ### 标题3 */}
    </div>
  );
};

export default BasicToolbar;
