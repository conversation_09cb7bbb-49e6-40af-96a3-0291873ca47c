/**
 * 基础工具栏样式
 * 简洁、现代的工具栏设计
 */

/* 工具栏容器 */
.basic-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 8px;
  flex-wrap: wrap;
}

/* 工具栏按钮组 */
.basic-toolbar .toolbar-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 工具栏按钮 */
.basic-toolbar button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 6px;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: transparent;
  color: #495057;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.15s ease;
  user-select: none;
}

/* 工具栏按钮悬停状态 */
.basic-toolbar button:hover {
  background-color: #e9ecef;
  border-color: #ced4da;
}

/* 工具栏按钮激活状态 */
.basic-toolbar button.is-active {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.basic-toolbar button.is-active:hover {
  background-color: #0056b3;
  border-color: #0056b3;
}

/* 工具栏按钮禁用状态 */
.basic-toolbar button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background-color: transparent;
  border-color: transparent;
  color: #6c757d;
}

.basic-toolbar button:disabled:hover {
  background-color: transparent;
  border-color: transparent;
}

/* 工具栏分隔符 */
.basic-toolbar .toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: #dee2e6;
  margin: 0 4px;
}

/* 特殊按钮样式 */
.basic-toolbar button strong {
  font-weight: bold;
}

.basic-toolbar button em {
  font-style: italic;
}

.basic-toolbar button s {
  text-decoration: line-through;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .basic-toolbar {
    padding: 6px;
    gap: 6px;
  }
  
  .basic-toolbar button {
    min-width: 28px;
    height: 28px;
    padding: 4px;
    font-size: 12px;
  }
  
  .basic-toolbar .toolbar-group {
    gap: 2px;
  }
  
  .basic-toolbar .toolbar-divider {
    height: 20px;
    margin: 0 2px;
  }
}

/* 工具栏在编辑状态下的样式 */
.editing .basic-toolbar {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 工具栏在查看状态下的样式 */
.viewing .basic-toolbar {
  display: none;
}
