/**
 * 标题编辑器组件
 * 严格按照 Tiptap 官方文档实现，专门用于便签标题编辑
 * 使用单行编辑模式，禁用换行和复杂格式化
 */

import React, { useCallback, useEffect } from "react";
import { useEditor, EditorContent } from "@tiptap/react";
import StarterKit from "@tiptap/starter-kit";
import Placeholder from "@tiptap/extension-placeholder";
import "./TitleEditor.css";

/**
 * 标题编辑器属性接口
 */
interface TitleEditorProps {
  /** 标题内容 */
  content?: string;
  /** 内容变化回调 */
  onChange?: (content: string) => void;
  /** 编辑器准备就绪回调 */
  onEditorReady?: (editor: any) => void;
  /** 占位符文本 */
  placeholder?: string;
  /** 是否自动聚焦 */
  autoFocus?: boolean;
  /** 是否可编辑 */
  editable?: boolean;
  /** 自定义类名 */
  className?: string;
  /** 点击事件 */
  onClick?: (e: React.MouseEvent) => void;
  /** 鼠标按下事件 */
  onMouseDown?: (e: React.MouseEvent) => void;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 标题属性 */
  title?: string;
  /** 按下 Enter 键的回调 */
  onEnter?: () => void;
  /** 按下 Escape 键的回调 */
  onEscape?: () => void;
  /** 按下 Tab 键的回调 */
  onTab?: () => void;
}

/**
 * 标题编辑器组件
 * 专门用于单行标题编辑，禁用换行和复杂格式化
 */
const TitleEditor: React.FC<TitleEditorProps> = ({
  content = "",
  onChange,
  onEditorReady,
  placeholder = "便签标题",
  autoFocus = false,
  editable = true,
  className = "",
  onClick,
  onMouseDown,
  style,
  title,
  onEnter,
  onEscape,
  onTab,
}) => {
  // 使用官方推荐的 useEditor hook，配置为单行编辑模式
  const editor = useEditor({
    // 使用 StarterKit 扩展，但禁用不需要的功能
    extensions: [
      StarterKit.configure({
        // 禁用换行相关功能，保持单行编辑
        paragraph: false,
        heading: false,
        blockquote: false,
        bulletList: false,
        orderedList: false,
        listItem: false,
        codeBlock: false,
        horizontalRule: false,
        // 修复 StarterKit 配置中 bold、italic、strike 和 code 的类型错误
        bold: {}, // 修正为对象类型
        italic: {}, // 修正为对象类型
        strike: {}, // 修正为对象类型
        code: {}, // 修正为对象类型
        // 禁用硬换行
        hardBreak: false,
      }),
      // 添加 Placeholder 扩展
      Placeholder.configure({
        placeholder: placeholder,
        emptyEditorClass: "is-editor-empty",
        emptyNodeClass: "is-empty",
        showOnlyWhenEditable: true,
        showOnlyCurrent: true,
      }),
    ],
    // 设置初始内容
    content: content,
    // 设置是否可编辑
    editable: editable,
    // 设置是否自动聚焦
    autofocus: autoFocus,
    // 内容更新回调
    onUpdate: ({ editor }) => {
      if (onChange) {
        // 获取纯文本内容，移除 HTML 标签
        const text = editor.getText();
        onChange(text);
      }
    },
    // 编辑器创建完成回调
    onCreate: ({ editor }) => {
      if (onEditorReady) {
        onEditorReady(editor);
      }
    },
    // 键盘事件处理
    editorProps: {
      handleKeyDown: (view, event) => {
        // 处理 Enter 键 - 阻止换行，触发回调
        if (event.key === "Enter") {
          event.preventDefault();
          if (onEnter) {
            onEnter();
          }
          return true;
        }

        // 处理 Escape 键
        if (event.key === "Escape") {
          event.preventDefault();
          if (onEscape) {
            onEscape();
          }
          return true;
        }

        // 处理 Tab 键
        if (event.key === "Tab") {
          event.preventDefault();
          if (onTab) {
            onTab();
          } else {
            // 默认行为：插入制表符
            const { from, to } = view.state.selection;
            view.dispatch(view.state.tr.insertText("\t", from, to));
          }
          return true;
        }

        return false;
      },
    },
  });

  // 当外部 content 发生变化时，更新编辑器内容
  useEffect(() => {
    if (editor && content !== editor.getText()) {
      editor.commands.setContent(content, { emitUpdate: false }); // 修正为对象类型
    }
  }, [content, editor]);

  // 当 editable 属性变化时，更新编辑器的可编辑状态
  useEffect(() => {
    if (editor) {
      editor.setEditable(editable);
    }
  }, [editable, editor]);

  // 处理点击事件
  const handleClick = useCallback(
    (e: React.MouseEvent) => {
      if (onClick) {
        onClick(e);
      }
    },
    [onClick]
  );

  // 处理鼠标按下事件
  const handleMouseDown = useCallback(
    (e: React.MouseEvent) => {
      if (onMouseDown) {
        onMouseDown(e);
      }
    },
    [onMouseDown]
  );

  // 如果编辑器还没有初始化完成，显示加载状态
  if (!editor) {
    return (
      <div
        className={`title-editor-loading ${className}`}
        style={style}
        title={title}
      >
        {placeholder}
      </div>
    );
  }

  return (
    <div
      className={`title-editor-container ${className}`}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      style={style}
      title={title}
    >
      {/* 使用官方的 EditorContent 组件渲染编辑器 */}
      <EditorContent editor={editor} className="title-editor-content" />
    </div>
  );
};

export default TitleEditor;
