/**
 * 迁移命令组件
 * 提供 UI 界面来执行数据迁移
 */

import React, { useState, useCallback } from "react";
import {
  Button,
  Progress,
  Card,
  Alert,
  Modal,
  Typography,
  Space,
  Divider,
  message,
} from "antd";
import {
  PlayCircleOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
// 临时禁用数据迁移功能
// import {
//   migrationUtils,
//   type MigrationProgress,
//   type MigrationOptions,
// } from "../../utils/dataMigrator";

// 临时类型定义
type MigrationProgress = {
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
  errors: Array<{ noteId: string; error: string }>;
};

import type { StickyNote } from "../types";

const { Title, Paragraph, Text } = Typography;

interface MigrationCommandProps {
  notes: StickyNote[];
  onMigrationComplete?: (migratedNotes: StickyNote[]) => void;
  visible?: boolean;
  onClose?: () => void;
}

export const MigrationCommand: React.FC<MigrationCommandProps> = ({
  notes,
  onMigrationComplete,
  visible = false,
  onClose,
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isMigrating, setIsMigrating] = useState(false);
  const [analysis, setAnalysis] = useState<any>(null);
  const [progress, setProgress] = useState<MigrationProgress | null>(null);
  const [migrationReport, setMigrationReport] = useState<string>("");
  const [, setMigratedNotes] = useState<StickyNote[]>([]);

  // 分析迁移影响
  const handleAnalyze = useCallback(async () => {
    setIsAnalyzing(true);
    try {
      // 临时禁用迁移分析
      // const result = migrationUtils.analyzeImpact(notes);
      const result = {
        totalNotes: notes.length,
        needsMigration: 0,
        estimatedTime: 0,
        potentialIssues: [],
      };
      setAnalysis(result);
      message.info("迁移功能已暂时禁用");
    } catch (error) {
      console.error("Analysis failed:", error);
    } finally {
      setIsAnalyzing(false);
    }
  }, [notes]);

  // 执行迁移
  const handleMigrate = useCallback(async () => {
    if (!analysis || analysis.needsMigration === 0) {
      return;
    }

    setIsMigrating(true);
    setProgress(null);
    setMigrationReport("");

    try {
      // 临时禁用迁移功能
      // const finalProgress = await migrationUtils.migrateNotes(notes, options);
      const finalProgress = {
        total: notes.length,
        processed: notes.length,
        succeeded: notes.length,
        failed: 0,
        errors: [],
      };
      setProgress(finalProgress);

      // 生成迁移报告
      // const report = migrationUtils.generateReport(finalProgress);
      const report = "迁移功能已暂时禁用";
      setMigrationReport(report);

      // 更新便签数据
      const updatedNotes = notes; // 暂时不做任何迁移
      // const updatedNotes = await Promise.all(
      //   notes.map(async (note) => {
      //     if (migrationUtils.needsMigration(note)) {
      //       const result = await migrationUtils.migrateNote(note);
      //       return result.success && result.note ? result.note : note;
      //     }
      //     return note;
      //   })
      // );

      setMigratedNotes(updatedNotes);

      // 通知父组件迁移完成
      if (onMigrationComplete && finalProgress.succeeded > 0) {
        onMigrationComplete(updatedNotes);
      }
    } catch (error) {
      console.error("Migration failed:", error);
    } finally {
      setIsMigrating(false);
    }
  }, [analysis, notes, onMigrationComplete]);

  // 重置状态
  const handleReset = useCallback(() => {
    setAnalysis(null);
    setProgress(null);
    setMigrationReport("");
    setMigratedNotes([]);
  }, []);

  // 关闭对话框
  const handleClose = useCallback(() => {
    handleReset();
    onClose?.();
  }, [handleReset, onClose]);

  return (
    <Modal
      title="内容格式迁移工具"
      open={visible}
      onCancel={handleClose}
      width={800}
      footer={null}
      destroyOnClose
    >
      <div style={{ maxHeight: "70vh", overflowY: "auto" }}>
        {/* 说明信息 */}
        <Alert
          message="关于内容格式迁移"
          description={
            <div>
              <Paragraph>
                此工具将现有的 Markdown 格式便签升级为 ProseMirror JSON
                格式，提供更好的编辑体验和数据一致性。
              </Paragraph>
              <ul>
                <li>任务列表、表格等复杂内容将使用原生 TipTap 处理</li>
                <li>迁移过程不会丢失任何数据</li>
                <li>保持向后兼容，未迁移的便签仍可正常使用</li>
                <li>迁移后的便签具有更好的性能和稳定性</li>
              </ul>
            </div>
          }
          type="info"
          icon={<InfoCircleOutlined />}
          style={{ marginBottom: 16 }}
        />

        {/* 分析阶段 */}
        {!analysis && (
          <Card title="第一步：分析迁移影响" style={{ marginBottom: 16 }}>
            <Paragraph>
              首先分析当前便签，确定哪些需要迁移以及可能的影响。
            </Paragraph>
            <Button
              type="primary"
              icon={<FileTextOutlined />}
              onClick={handleAnalyze}
              loading={isAnalyzing}
            >
              开始分析
            </Button>
          </Card>
        )}

        {/* 分析结果 */}
        {analysis && !isMigrating && !progress && (
          <Card title="分析结果" style={{ marginBottom: 16 }}>
            <Space direction="vertical" style={{ width: "100%" }}>
              <div>
                <Text strong>总便签数量: </Text>
                <Text>{analysis.totalNotes}</Text>
              </div>
              <div>
                <Text strong>需要迁移: </Text>
                <Text
                  type={analysis.needsMigration > 0 ? "warning" : "success"}
                >
                  {analysis.needsMigration}
                </Text>
              </div>
              {analysis.needsMigration > 0 && (
                <>
                  <div>
                    <Text strong>包含任务列表: </Text>
                    <Text>{analysis.hasTaskLists}</Text>
                  </div>
                  <div>
                    <Text strong>包含表格: </Text>
                    <Text>{analysis.hasTables}</Text>
                  </div>
                  <div>
                    <Text strong>包含复杂HTML: </Text>
                    <Text>{analysis.hasComplexHTML}</Text>
                  </div>
                </>
              )}
            </Space>

            <Divider />

            {analysis.needsMigration > 0 ? (
              <Space>
                <Button
                  type="primary"
                  icon={<PlayCircleOutlined />}
                  onClick={handleMigrate}
                  disabled={isMigrating}
                >
                  开始迁移 ({analysis.needsMigration} 个便签)
                </Button>
                <Button onClick={handleReset}>重新分析</Button>
              </Space>
            ) : (
              <Alert
                message="无需迁移"
                description="所有便签都已经是最新格式，无需进行迁移。"
                type="success"
                showIcon
              />
            )}
          </Card>
        )}

        {/* 迁移进度 */}
        {isMigrating && progress && (
          <Card title="迁移进度" style={{ marginBottom: 16 }}>
            <Progress
              percent={
                progress.total > 0
                  ? Math.round((progress.processed / progress.total) * 100)
                  : 0
              }
              status={isMigrating ? "active" : "normal"}
              strokeColor={{
                "0%": "#108ee9",
                "100%": "#87d068",
              }}
            />
            <Space
              direction="vertical"
              style={{ width: "100%", marginTop: 16 }}
            >
              <div>
                <Text>
                  进度: {progress.processed} / {progress.total}
                </Text>
              </div>
              <div>
                <Text type="success">成功: {progress.succeeded}</Text>
                {progress.failed > 0 && (
                  <Text type="danger" style={{ marginLeft: 16 }}>
                    失败: {progress.failed}
                  </Text>
                )}
              </div>
            </Space>
          </Card>
        )}

        {/* 迁移完成 */}
        {progress && !isMigrating && (
          <Card title="迁移完成" style={{ marginBottom: 16 }}>
            <Alert
              message={`迁移完成！成功迁移 ${progress.succeeded} 个便签`}
              description={
                progress.failed > 0
                  ? `${progress.failed} 个便签迁移失败，请查看详细报告。`
                  : "所有需要迁移的便签都已成功升级。"
              }
              type={progress.failed > 0 ? "warning" : "success"}
              showIcon
              style={{ marginBottom: 16 }}
            />

            {migrationReport && (
              <div>
                <Title level={5}>迁移报告</Title>
                <pre
                  style={{
                    backgroundColor: "#f5f5f5",
                    padding: 12,
                    borderRadius: 4,
                    fontSize: 12,
                    maxHeight: 200,
                    overflow: "auto",
                    whiteSpace: "pre-wrap",
                  }}
                >
                  {migrationReport}
                </pre>
              </div>
            )}

            <Space style={{ marginTop: 16 }}>
              <Button type="primary" onClick={handleClose}>
                完成
              </Button>
              <Button onClick={handleReset}>重新运行</Button>
            </Space>
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default MigrationCommand;
