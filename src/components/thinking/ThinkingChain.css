/* 思维链容器样式 */
.thinking-chain-container {
  margin: 0; /* 去掉上下 margin */
  border-radius: 8px;
  background: #fafafa;
  border: 1px solid #f0f0f0;
}

.thinking-chain-container.compact {
  margin-bottom: 12px;
  background: transparent;
  border: none;
}

/* 思维链头部样式 */
.thinking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  user-select: none;
  /* margin: bottom 8px; */
}

.thinking-header:hover {
  background: #ffffffcb;
}

.thinking-chain-container.compact .thinking-header {
  padding: 8px 12px;
}

.thinking-expand-icon {
  font-size: 12px;
  color: #8c8c8c;
  transition: transform 0.2s ease;
}

/* 思维链头部样式 */
.thinking-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.thinking-title {
  display: flex;
  align-items: center;
  flex: 1;
}

.thinking-time {
  font-size: 12px;
  margin-left: 8px;
}

/* 思维链内容样式 */
.thinking-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* 原始提示词卡片样式 */
.thinking-prompt-card {
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

.thinking-prompt-card .ant-card-body {
  padding: 12px;
}

/* 思维链统计信息样式 */
.thinking-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  align-items: center;
  padding: 8px 16px;
  background: #f9f9f9;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  margin: 0 16px 8px 16px;
}

/* 思维链步骤列表样式 */
.thinking-steps-list {
  margin: 16px 0;
}

.thinking-chain-container.compact .thinking-steps-list {
  margin: 12px 0;
}

/* 思维链步骤项样式 */
.thinking-step-item {
  position: relative;
  margin-bottom: 16px;
}

.thinking-chain-container.compact .thinking-step-item {
  margin-bottom: 12px;
}

/* 思维链步骤头部样式 */
.thinking-step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  position: relative;
  z-index: 1;
}

.thinking-chain-container.compact .thinking-step-header {
  margin-bottom: 6px;
}

/* 带图标的步骤标签样式 */
.thinking-step-tag-with-icon {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 13px;
}

.thinking-chain-container.compact .thinking-step-tag-with-icon {
  padding: 3px 10px;
  font-size: 12px;
  gap: 4px;
}

/* 思维链步骤内容样式 - 突出内容 */
.thinking-step-content {
  margin-left: 16px; /* 减少左边距，让内容更靠近连接线 */
  margin-right: 8px;
  padding: 6px 0;
  padding-left: 12px; /* 添加左内边距，创建视觉层次 */
  border-left: 2px solid #f0f0f0; /* 添加左边框，增强视觉引导 */
}

.thinking-chain-container.compact .thinking-step-content {
  margin-left: 12px;
  padding: 4px 0;
  padding-left: 8px;
}

.thinking-step-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px; /* 减少底部边距，让标签更贴近内容 */
  position: relative;
  z-index: 1;
}

.thinking-chain-container.compact .thinking-step-header {
  margin-bottom: 3px;
}

/* 带图标的步骤标签样式 - 无背景，颜色在文本和图标上 */
.thinking-step-tag-with-icon {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-weight: 500;
  padding: 0; /* 移除内边距 */
  border-radius: 0; /* 移除圆角 */
  font-size: 13px; /* 从11px增加到13px，比思维链文本稍小但更显眼 */
  background: transparent !important; /* 强制透明背景 */
  border: none !important; /* 移除边框 */
  /* 覆盖Ant Design的默认样式 */
}

/* 覆盖Ant Design Tag的默认样式 */
.thinking-step-tag-with-icon.ant-tag {
  background: transparent !important;
  border: none !important;
  color: inherit !important;
  padding: 0 !important;
  margin: 0 !important;
}

.thinking-chain-container.compact .thinking-step-tag-with-icon {
  font-size: 12px; /* 紧凑模式下也适当增大 */
  gap: 3px;
}

.thinking-chain-container.compact
  .thinking-step-tag-with-icon
  span:first-child {
  font-size: 13px; /* 紧凑模式下图标稍大于文字 */
}

/* 标签内的图标和文本样式 */
.thinking-step-tag-with-icon span {
  display: inline-flex;
  align-items: center;
}

/* 图标样式 */
.thinking-step-tag-with-icon span:first-child {
  font-size: 14px; /* 图标比文字稍大一点 */
}

/* 文本样式 */
.thinking-step-tag-with-icon span:last-child {
  font-size: inherit;
  font-weight: inherit;
}

/* 保留原有的step-tag样式作为备用 */
.thinking-step-tag {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 4px;
  margin: 0;
}

.thinking-step-time {
  font-size: 10px;
  display: flex;
  align-items: center;
  color: #8c8c8c; /* 调整为适中的灰色 */
  white-space: nowrap;
  opacity: 1; /* 恢复完全不透明 */
}

.thinking-chain-container.compact .thinking-step-time {
  font-size: 9px;
}

.thinking-step-text {
  font-size: 14px;
  line-height: 1.6;
  color: #262626; /* 更深的颜色，让内容更突出 */
  margin: 0;
  font-weight: 400;
}

.thinking-chain-container.compact .thinking-step-text {
  font-size: 13px;
  line-height: 1.5;
}

/* 思维过程区域样式 */
.thinking-process-section {
  background: #ffffff89;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 16px 8px 16px;
  border: 1px solid #f0f0f0;
  /* 滚动容器设置 */
  max-height: 300px; /* 默认最大高度 */
  overflow-y: auto; /* 允许垂直滚动 */
  overflow-x: hidden; /* 隐藏水平滚动条 */
  /* 滚动条样式优化 - 默认显示 */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #d9d9d9 transparent; /* Firefox - 默认显示 */
}

/* Webkit浏览器的滚动条样式 - 默认显示 */
.thinking-process-section::-webkit-scrollbar {
  width: 6px;
}

.thinking-process-section::-webkit-scrollbar-track {
  background: transparent;
}

.thinking-process-section::-webkit-scrollbar-thumb {
  background-color: #d9d9d9; /* 默认显示滚动条 */
  border-radius: 3px;
}

.thinking-process-section::-webkit-scrollbar-thumb:hover {
  background-color: #bfbfbf; /* 悬浮时加深颜色 */
}

/* 最终答案区域样式 */
.thinking-final-section {
  position: relative;
}

.thinking-final-section::before {
  content: "";
  position: absolute;
  top: -12px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #52c41a 50%,
    transparent 100%
  );
  border-radius: 1px;
}

/* 最终答案卡片样式 */
.thinking-final-answer {
  background: linear-gradient(135deg, #f6ffed 0%, #f0f9e8 100%);
  border: 2px solid #52c41a;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(82, 196, 26, 0.15);
  position: relative;
  overflow: hidden;
}

.thinking-final-answer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #52c41a, #73d13d, #52c41a);
}

.thinking-final-answer .ant-card-body {
  padding: 20px;
  padding-top: 24px;
}

/* 最终答案标题样式 */
.thinking-final-header {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid #d9f7be;
}

/* 紧凑模式样式调整 */
.thinking-chain-container.compact .thinking-stats {
  padding: 8px;
  gap: 12px;
}

/* 紧凑模式下的步骤内容样式已在上面定义 */

.thinking-chain-container.compact .thinking-step-text {
  font-size: 12px;
}

.thinking-chain-container.compact .thinking-prompt-card .ant-card-body {
  padding: 8px;
}

.thinking-chain-container.compact .thinking-final-answer .ant-card-body {
  padding: 12px;
  padding-top: 16px;
}

.thinking-chain-container.compact .thinking-process-section {
  padding: 12px;
}

.thinking-chain-container.compact .thinking-final-header {
  margin-bottom: 6px;
  padding-bottom: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .thinking-stats {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .thinking-stats-tags {
    margin-left: 0;
    margin-top: 4px;
  }

  .thinking-step-content {
    margin-left: 8px;
  }

  .thinking-step-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

/* 动画效果 */
.thinking-step-tag-with-icon {
  transition: all 0.2s ease;
}

.thinking-step-tag-with-icon:hover {
  opacity: 0.8; /* 悬停时稍微降低透明度 */
  cursor: default;
}

/* 便签中的思维链特殊样式 - 更新以适应新的容器结构 */
.thinking-chain-in-note {
  margin: 0; /* 移除所有边距，由外层容器控制 */
  padding: 0; /* 移除内边距，避免重复 */
}

/* 基础思维链容器样式 - 使用CSS变量实现动态高度 */
.thinking-chain-in-note .thinking-process-section {
  /* 使用CSS变量动态设置高度，由JavaScript根据便签尺寸计算 */
  max-height: var(--thinking-max-height, 120px); /* 默认120px，可通过变量覆盖 */
  min-height: var(--thinking-min-height, 60px); /* 默认60px，可通过变量覆盖 */
  margin: 0 8px; /* 减小左右margin，为最小便签宽度留出更多空间 */
  padding: 8px; /* 减小内边距 */
  margin-bottom: 8px; /* 与便签底部保持适当间距 */
}

.thinking-chain-in-note.compact .thinking-process-section {
  /* 紧凑模式下的高度也使用变量控制 */
  max-height: var(--thinking-max-height-compact, 100px); /* 默认100px */
  min-height: var(--thinking-min-height-compact, 50px); /* 默认50px */
  margin: 0 0px; /* 更小的左右margin */
  padding: 6px;
  margin-bottom: 6px; /* 与便签底部保持适当间距 */
  margin-top: 6px; /* 增加上 margin 6px */
}

/* 便签中的思维链步骤列表样式优化 */
.thinking-chain-in-note .thinking-steps-list {
  margin: 0; /* 去掉上下margin */
}

.thinking-chain-in-note .thinking-stats {
  margin: 0 8px; /* 减小左右margin以适应最小便签 */
  padding: 4px 6px; /* 减少内边距 */
  margin-bottom: 4px; /* 与思维链过程区域保持小间距 */
}

/* 便签中的思维链步骤项优化 */
.thinking-chain-in-note .thinking-step-item {
  margin-bottom: 8px; /* 进一步减少步骤间距以节省空间 */
}

.thinking-chain-in-note .thinking-step-content {
  margin-left: 16px; /* 减少左边距以节省水平空间 */
  margin-right: 4px; /* 减少右边距 */
  padding: 4px 0; /* 减少内边距以节省垂直空间 */
}

.thinking-chain-in-note .thinking-step-text {
  font-size: 11px; /* 进一步减小字体以适应小空间 */
  line-height: 1.3; /* 减小行高以节省垂直空间 */
}

.thinking-chain-in-note .thinking-step-tag-with-icon {
  font-size: 11px; /* 便签中也适当增大 */
  gap: 3px;
}

.thinking-chain-in-note .thinking-step-tag-with-icon span:first-child {
  font-size: 12px; /* 便签中的图标也相应增大 */
}

/* 深色主题支持 */
@media (prefers-color-scheme: dark) {
  .thinking-chain-container {
    background: #1f1f1f;
    border-color: #303030;
  }

  .thinking-collapse .ant-collapse-header:hover {
    background: #262626;
  }

  .thinking-stats {
    background: #262626;
    border-color: #303030;
  }

  .thinking-prompt-card {
    background: #111b26;
    border-color: #1668dc;
  }

  .thinking-process-section {
    background: #262626;
    border-color: #434343;
  }

  .thinking-final-answer {
    background: linear-gradient(135deg, #162312 0%, #1f2f1f 100%);
    border-color: #389e0d;
    box-shadow: 0 2px 8px rgba(56, 158, 13, 0.2);
  }

  .thinking-final-answer::before {
    background: linear-gradient(90deg, #389e0d, #52c41a, #389e0d);
  }

  .thinking-final-header {
    border-bottom-color: #274916;
  }

  .thinking-step-text {
    color: #d9d9d9;
  }
}

/* 针对最小便签尺寸的特殊优化 - 现在主要通过动态计算处理，保留必要的样式优化 */
.sticky-note[style*="width: 250px"]
  .thinking-chain-in-note
  .thinking-process-section,
.sticky-note[style*="width: 251px"]
  .thinking-chain-in-note
  .thinking-process-section,
.sticky-note[style*="width: 252px"]
  .thinking-chain-in-note
  .thinking-process-section {
  margin: 0 4px !important; /* 最小左右边距 */
  padding: 4px !important; /* 最小内边距 */
  margin-bottom: 4px !important;
}

/* 最小便签尺寸时的字体和间距进一步优化 */
.sticky-note[style*="width: 250px"] .thinking-chain-in-note .thinking-step-text,
.sticky-note[style*="height: 230px"]
  .thinking-chain-in-note
  .thinking-step-text {
  font-size: 10px !important;
  line-height: 1.2 !important;
}

.sticky-note[style*="width: 250px"] .thinking-chain-in-note .thinking-step-item,
.sticky-note[style*="height: 230px"]
  .thinking-chain-in-note
  .thinking-step-item {
  margin-bottom: 4px !important;
}

.sticky-note[style*="width: 250px"]
  .thinking-chain-in-note
  .thinking-step-content,
.sticky-note[style*="height: 230px"]
  .thinking-chain-in-note
  .thinking-step-content {
  margin-left: 12px !important;
  margin-right: 2px !important;
  padding: 2px 0 !important;
}
