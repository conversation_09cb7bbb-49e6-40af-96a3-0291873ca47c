/* 工具栏磨砂效果变量已移至全局 src/styles/glass-effects.css */

.infinite-canvas-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: visible;
  background-color: #f8fafc;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    "Helvetica Neue", Arial, sans-serif;
  cursor: default;

  /* 使用CSS变量存储关键值 */
  --canvas-scale: 1;
  --canvas-offset-x: 0px;
  --canvas-offset-y: 0px;
  --canvas-background: #fcfcfa;
  --grid-visible: 1;
  --small-grid-size: 10px;
  --large-grid-size: 50px;
  --small-grid-color: rgba(226, 232, 240, 0.2);
  --large-grid-color: rgba(203, 213, 225, 0.4);
  --paper-texture-opacity: 0.03;

  /* 性能优化 - GPU加速 */
  will-change: transform;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transform: translate3d(0, 0, 0);
  -webkit-transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 拖拽时的光标状态 */
.infinite-canvas-container.dragging {
  cursor: move;
}

/* 中键拖拽时的光标状态 */
.infinite-canvas-container.middle-button-dragging {
  cursor: all-scroll; /* 使用全方向滚动光标 */
}

/* 右键拖拽时的光标状态 */
.infinite-canvas-container.right-button-dragging {
  cursor: move; /* 使用移动光标 */
}

/* 移动模式的光标状态 */
.infinite-canvas-container.move-mode {
  cursor: grab;
}

.infinite-canvas-container.move-mode.dragging {
  cursor: grabbing;
}

.canvas-toolbar {
  position: fixed; /* 相对于视口定位 */
  top: 50%; /* 垂直居中 */
  right: 20px; /* 距离右侧一段距离 */
  transform: translateY(-50%); /* 精确垂直居中 */
  z-index: 100;
  padding: 8px 6px 16px 6px; /* 减少顶部内边距到8px，底部保持16px */

  /* 半透明磨砂效果 - 使用CSS变量 */
  background: var(--toolbar-glass-bg);
  backdrop-filter: var(--toolbar-glass-blur);
  -webkit-backdrop-filter: var(--toolbar-glass-blur);
  border: 1px solid var(--toolbar-glass-border);
  box-shadow: var(--toolbar-glass-shadow);

  border-radius: 32px; /* 保持药丸形状 */
  display: flex; /* 用于内部元素对齐 */
  align-items: center; /* 水平对齐内部元素 */
  flex-direction: column; /* 修改为垂直排列 */

  /* 性能优化 */
  will-change: transform;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

/* 工具栏浏览器降级方案 */
@supports not (backdrop-filter: blur(12px)) {
  .canvas-toolbar {
    background: var(--toolbar-glass-fallback);
  }
}

/* 新增：小提示文本样式 */
.canvas-tooltip-help {
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  margin-top: 6px;
  white-space: nowrap;
}

.zoom-indicator {
  font-size: 12px; /* 增大字体大小 */
  color: #666;
  font-weight: 600; /* 增加字重，更醒目 */
  margin-top: 8px; /* 改为上边距，适应垂直布局 */
  padding: 0; /* 移除内边距 */
  background: none; /* 移除背景 */
  border: none; /* 移除边框 */
  text-align: center; /* 文字居中 */
  line-height: 1.1; /* 紧凑的行高 */
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: auto; /* 移除最小宽度限制 */
  width: fit-content; /* 自适应内容宽度 */
}

/* 缩放指示器内部元素样式 */
.zoom-indicator div {
  margin: 0;
  padding: 0;
  line-height: 1;
}

/* 移动模式指示器 */
.move-mode-indicator {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  backdrop-filter: blur(8px);
  animation: slide-in-down 0.3s ease-out;
}

@keyframes slide-in-down {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 工具栏分隔线 */
.canvas-toolbar .toolbar-divider {
  width: 20px; /* 改为水平线 */
  height: 1px; /* 改为水平线 */
  background-color: rgba(0, 0, 0, 0.1);
  margin: 4px 0; /* 改为上下边距 */
}

/* 画布背景区域 - 包含网格，不应用变换 */
.canvas-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--canvas-background);
  pointer-events: none;
  z-index: 1;
  overflow: visible;
}

.infinite-canvas {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2; /* 确保在网格之上 */
  pointer-events: none; /* 让事件穿透到容器 */
  transform-origin: 0 0;
  /* 确保便签可以显示在画布边界外 */
  overflow: visible;

  /* 性能优化 - GPU加速 */
  will-change: transform;
  backface-visibility: hidden;
  transform-style: preserve-3d;
  perspective: 1000px;
  -webkit-perspective: 1000px;
  -webkit-backface-visibility: hidden;

  /* 额外的性能优化 - 完全移除containment以避免裁剪问题 */
  /* contain: layout style; */
  /* isolation: isolate; */

  /* 使用CSS变量进行变换，但便签容器只平移不缩放 */
  /* 避免CSS transform缩放导致的文本模糊问题 */
  transform: translate3d(
    var(--content-offset-x, 0px),
    var(--content-offset-y, 0px),
    0
  );
  /* 移除 scale(var(--canvas-scale, 1)) - 便签现在通过字体大小调整来适应缩放 */
}

/* 便签恢复点击能力 */
.infinite-canvas .sticky-note {
  pointer-events: auto;
}

.canvas-content {
  width: 100%;
  height: 100%;
  transform-origin: 0 0;
  position: relative;
  will-change: transform; /* 优化性能 */

  /* 便签内容容器只平移不缩放，避免文本模糊 */
  transform: translate3d(
    var(--content-offset-x, 0px),
    var(--content-offset-y, 0px),
    0
  );
  /* 移除缩放变换，改为通过字体大小和便签尺寸调整来模拟缩放效果 */
}

/* 便签容器 - 独立于画布变换 */
.sticky-notes-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none; /* 允许点击穿透到画布 */
  transform-origin: 0 0;
  will-change: transform;

  /* 便签容器跟随画布平移，但不缩放 */
  transform: translate3d(
    var(--content-offset-x, 0px),
    var(--content-offset-y, 0px),
    0
  );
}

/* 便签本身恢复点击能力 */
.sticky-notes-container .sticky-note {
  pointer-events: auto;
}

/* 统一的网格样式 - 简化版本 */
.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: var(--grid-visible, 1);

  /* 只显示小网格，简洁清晰 */
  background-image: linear-gradient(
      var(--small-grid-color) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, var(--small-grid-color) 1px, transparent 1px);

  background-size: calc(var(--small-grid-size) * var(--canvas-scale))
    calc(var(--small-grid-size) * var(--canvas-scale));

  background-position: var(--canvas-offset-x) var(--canvas-offset-y);

  /* 添加大网格效果 - 使用伪元素 */
}

.canvas-grid::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: var(--grid-visible, 1);

  /* 大网格线 - 更粗更明显 */
  background-image: linear-gradient(
      var(--large-grid-color) 1px,
      transparent 1px
    ),
    linear-gradient(90deg, var(--large-grid-color) 1px, transparent 1px);

  background-size: calc(var(--large-grid-size) * var(--canvas-scale))
    calc(var(--large-grid-size) * var(--canvas-scale));

  background-position: var(--canvas-offset-x) var(--canvas-offset-y);
}

/* 添加轴线，显示中心点 */
.canvas-axis {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 3;
}

.canvas-axis::before,
.canvas-axis::after {
  content: "";
  position: absolute;
  background-color: rgba(99, 102, 241, 0.1);
  z-index: 2;
}

.canvas-axis::before {
  width: 2px;
  height: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.canvas-axis::after {
  width: 100%;
  height: 2px;
  top: 50%;
  transform: translateY(-50%);
}

/* 添加画布阴影效果 */
.canvas-shadow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  box-shadow: inset 0 0 40px rgba(51, 65, 85, 0.04);
  pointer-events: none;
  z-index: 5;
  border-radius: 2px;
}

/* 添加过渡效果，但注意避免对频繁变化的属性使用 */
.canvas-toolbar button {
  transition: background-color 0.2s ease, color 0.2s ease,
    border-color 0.2s ease;
}

/* 禁用文本选择 */
.infinite-canvas-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 滚动条隐藏 */
.infinite-canvas::-webkit-scrollbar {
  display: none;
}

.infinite-canvas {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* 第一个按钮去掉上间隔 */
.canvas-toolbar .first-button {
  margin-top: -16px; /* 进一步减少顶部间距 */
}

/* 工具栏按钮美化 */
.canvas-toolbar .ant-btn {
  width: 32px; /* 修正按钮尺寸为32x32 */
  height: 32px; /* 保持32px高度 */
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 15px; /* 稍微减小图标大小 */
  border-radius: 8px; /* 按钮圆角 */
  transition: all 0.2s ease; /* 添加过渡效果 */
}

.canvas-toolbar .ant-btn:hover {
  background-color: rgba(22, 119, 255, 0.1); /* 更明显的悬停效果 */
  color: #1677ff;
  transform: translateY(-1px); /* 轻微上浮效果 */
}

.canvas-toolbar .ant-btn:active {
  background-color: rgba(22, 119, 255, 0.15);
  transform: translateY(0); /* 按下时回到原位 */
}

.canvas-toolbar .ant-btn[disabled] {
  color: rgba(0, 0, 0, 0.25);
  background-color: transparent;
  transform: none; /* 禁用状态不应用变换 */
}

/* 缩放指示器呼吸效果 */
@keyframes subtle-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 1;
  }
}

.zoom-change .zoom-indicator {
  animation: subtle-pulse 1.5s ease;
}
