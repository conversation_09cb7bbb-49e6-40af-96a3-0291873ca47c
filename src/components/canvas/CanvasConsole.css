/* 移除动画效果 */
.canvas-console {
  position: fixed; /* 相对于视口定位 */
  bottom: 24px; /* 距离底部一段距离 */
  left: 50%; /* 水平居中 */
  transform: translateX(-50%); /* 精确水平居中 */
  z-index: 99; /* 稍低于工具栏，避免冲突 */
  max-width: 480px; /* 减小最大宽度 */
  width: 75%; /* 减小响应式宽度 */
  min-width: 320px; /* 减小最小宽度 */

  /* 确保不被侧边栏遮挡 */
  margin-left: auto;
  margin-right: auto;
}

.console-container {
  display: flex;
  align-items: center;
  justify-content: space-between; /* 添加 space-between 布局 */
  padding: 8px 10px; /* 调整内边距使按钮和输入框高度协调 */
  background: #ffffff; /* 纯白色背景 */
  border-radius: 16px;
  border: 1px solid rgba(0, 0, 0, 0.06); /* 默认边框 */
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08), 0 1px 3px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s ease, box-shadow 0.3s ease; /* 添加过渡效果 */
}

/* 移除悬停和聚焦状态的交互效果 */

/* 输入框容器 */
.console-input-container {
  flex: 1;
  margin-right: 10px; /* 添加右侧间距，为外部按钮留出空间 */
  display: flex; /* 添加flex布局 */
  align-items: center; /* 垂直居中 */
}

/* 输入框样式 - 去除边线只保留底色 */
.console-input {
  border-radius: 12px !important;
  border: none !important; /* 去掉边线 */
  background: #f5f5f5 !important; /* 修改底色为#f5f5f5 */
  font-size: 12px !important;
  padding: 8px 12px !important; /* 调整内边距 */
  height: auto !important; /* 取消固定高度 */
}

.console-input::placeholder {
  color: #999999 !important;
  font-weight: 400 !important;
}

/* 内联按钮样式 - 无交互效果 */
.send-button,
.add-button-inline,
.ai-send-button {
  border-radius: 50% !important;
  margin-right: 2px !important;
  position: relative !important;
  overflow: hidden !important;
  width: 28px !important;
  height: 28px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* AI发送按钮 - 基本样式无交互 */
.ai-send-button {
  background: linear-gradient(135deg, #1677ff, #0958d9) !important;
  border: none !important;
  color: white !important;
  font-size: 16px !important;
}

/* 演示模式发送按钮 - 基本样式无交互 */
.send-button {
  color: #1677ff !important;
  background: rgba(22, 119, 255, 0.08) !important;
  border: 1px solid rgba(22, 119, 255, 0.2) !important;
  font-size: 16px !important;
}

/* 手动添加按钮 - 基本样式无交互 */
.add-button-inline {
  color: #1677ff !important;
  background: rgba(22, 119, 255, 0.05) !important;
  border: 1px solid rgba(22, 119, 255, 0.15) !important;
  font-size: 16px !important;
}

.send-button:disabled,
.ai-send-button:disabled {
  color: rgba(22, 119, 255, 0.3) !important;
  background: rgba(22, 119, 255, 0.05) !important;
  border-color: rgba(22, 119, 255, 0.1) !important;
}

/* 状态指示器样式 */
.generation-status {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-bottom: 12px;
  z-index: 1000;
}

.status-content {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 13px;
  white-space: nowrap;
  animation: statusFadeIn 0.3s ease;
}

@keyframes statusFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 外部按钮容器 */
.console-external-buttons {
  display: flex;
  align-items: center;
  padding-left: 0; /* 移除左侧内边距 */
}

/* 外部便签生成按钮 */
.external-button {
  border-radius: 50% !important;
  width: 38px !important; /* 确保宽高一致 */
  height: 38px !important; /* 确保宽高一致 */
  min-width: 38px !important; /* 防止按钮被压缩 */
  min-height: 38px !important; /* 防止按钮被压缩 */
  padding: 0 !important; /* 移除内边距 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  margin-left: 0 !important; /* 移除左侧外边距，去掉按钮左侧空白区域 */
  font-size: 18px !important;
  flex-shrink: 0 !important; /* 防止按钮被挤压 */
  box-sizing: border-box !important; /* 确保边框不影响尺寸 */
}

/* AI生成按钮 - 外部样式 */
.ai-external-button {
  background: #1677ff !important; /* 纯色背景 */
  border: none !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.45),
    0 0 0 1px rgba(24, 144, 255, 0.2) !important; /* 添加阴影和边缘轮廓 */
  transition: all 0.2s ease !important; /* 添加过渡效果 */
}

/* AI智能处理按钮 - 独立的绿色按钮样式 */
.ai-smart-process-button {
  background: #52c41a !important; /* 绿色背景 */
  border: none !important;
  color: white !important;
  box-shadow: 0 2px 6px rgba(82, 196, 26, 0.45),
    0 0 0 1px rgba(82, 196, 26, 0.2) !important; /* 绿色阴影和边缘轮廓 */
  transition: all 0.2s ease !important; /* 添加过渡效果 */
}

/* 创建空白便签按钮 - 外部样式 */
.add-external-button {
  background: #4096ff !important; /* 更鲜明的蓝色背景 */
  border: none !important; /* 移除边框 */
  color: white !important; /* 改为白色图标，增加对比度 */
  box-shadow: 0 2px 6px rgba(24, 144, 255, 0.45),
    0 0 0 1px rgba(24, 144, 255, 0.2) !important; /* 增强阴影并添加边缘轮廓 */
  transition: all 0.2s ease !important; /* 添加过渡效果 */
}

/* 创建空白便签按钮 - 悬停状态 */
.add-external-button:hover {
  background: #2a7ae2 !important; /* 悬停时更深的蓝色 */
  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.55),
    0 0 0 1px rgba(24, 144, 255, 0.3) !important; /* 悬停时进一步增强阴影和边缘 */
  transform: scale(1.05) !important; /* 轻微放大效果 */
}

/* AI生成按钮 - 悬停状态 */
.ai-external-button:hover {
  background: #0958d9 !important; /* 悬停时深蓝色 */
  box-shadow: 0 4px 10px rgba(24, 144, 255, 0.55),
    0 0 0 1px rgba(24, 144, 255, 0.3) !important; /* 悬停时进一步增强阴影和边缘 */
  transform: scale(1.05) !important; /* 轻微放大效果 */
}

/* AI智能处理按钮 - 悬停状态 */
.ai-smart-process-button:hover {
  background: #389e0d !important; /* 悬停时深绿色 */
  box-shadow: 0 4px 10px rgba(82, 196, 26, 0.55),
    0 0 0 1px rgba(82, 196, 26, 0.3) !important; /* 悬停时绿色阴影增强 */
  transform: scale(1.05) !important; /* 轻微放大效果 */
}

/* AI智能处理按钮 - 需要输入时的无法使用悬浮效果 */
.ai-smart-process-button.requires-input:hover {
  background: #52c41a !important; /* 保持原绿色，不变深 */
  box-shadow: 0 2px 6px rgba(255, 77, 79, 0.3), 0 0 0 2px rgba(255, 77, 79, 0.2) !important; /* 红色警告边框 */
  transform: scale(1.02) !important; /* 轻微放大，但比正常状态小 */
  cursor: not-allowed !important; /* 禁用光标 */
  animation: shake 0.5s ease-in-out !important; /* 添加轻微摇摆动画 */
}

/* 摇摆动画 */
@keyframes shake {
  0%,
  100% {
    transform: scale(1.02) translateX(0);
  }
  25% {
    transform: scale(1.02) translateX(-1px);
  }
  75% {
    transform: scale(1.02) translateX(1px);
  }
}

/* 覆盖 Ant Design 按钮的默认样式，确保圆形按钮 */
.console-external-buttons .ant-btn-circle {
  border-radius: 50% !important;
  width: 38px !important;
  height: 38px !important;
  min-width: 38px !important;
  min-height: 38px !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 修复按钮图标居中问题 */
.console-external-buttons .ant-btn-circle .anticon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 18px !important;
  line-height: 1 !important;
  margin: 0 !important;
}

/* 响应式设计 - 基本适配无动画 */
@media (max-width: 768px) {
  .canvas-console {
    width: 85%;
    min-width: 280px;
    bottom: 20px;
  }

  .console-container {
    padding: 6px 12px;
    border-radius: 14px;
  }

  .console-input {
    font-size: 12px !important;
    padding: 6px 12px !important;
    border-radius: 10px !important;
  }

  /* 移动端按钮优化 */
  .external-button,
  .console-external-buttons .ant-btn-circle {
    width: 34px !important;
    height: 34px !important;
    min-width: 34px !important;
    min-height: 34px !important;
    font-size: 16px !important;
    margin-left: 0 !important; /* 移除左侧外边距，保持与桌面版一致 */
  }

  .console-external-buttons .ant-btn-circle .anticon {
    font-size: 16px !important;
  }
}

/* 移除加载动画效果 */

/* 移除AI生成状态下的特殊样式 */
