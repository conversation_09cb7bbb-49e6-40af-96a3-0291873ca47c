# 内容转换器测试指南

## 概述

本文档介绍如何使用新的 TipTap 原生内容转换器测试工具。

## 长期方案实施总结

✅ **已完成的工作：**

1. **创建原生转换工具** (`src/utils/contentConverter.ts`)

   - 使用 TipTap 的 `generateJSON` 和 `generateHTML` 原生 API
   - 支持 Markdown ↔ ProseMirror JSON 双向转换
   - 包含错误处理和回退机制

2. **更新编辑器组件** (`src/components/notes/WysiwygEditor.tsx`)

   - 集成任务列表扩展 (`@tiptap/extension-task-list`, `@tiptap/extension-task-item`)
   - 添加表格支持 (`@tiptap/extension-table`)
   - 保持与现有代码逻辑的兼容性

3. **更新存储逻辑**

   - 新增 `contentAdapter.ts` 用于格式适配
   - 更新类型定义支持新的 `contentFormat` 字段
   - 保持向后兼容性

4. **创建迁移工具**

   - `dataMigrator.ts` - 批量数据迁移工具
   - `MigrationCommand.tsx` - 用户友好的迁移界面
   - 包含进度跟踪和回滚功能

5. **测试验证**
   - `ContentConverterTest.tsx` - 完整的转换功能测试
   - `TestPage.tsx` - 测试工具集合页面
   - 多种预设测试用例

## 访问测试工具

### 方法 1: URL 参数

在浏览器中访问：

```
http://localhost:5173/?test=conversion-test
```

### 方法 2: 开发服务器

如果开发服务器正在运行，直接访问测试 URL 即可。

## 测试功能

### 1. 内容转换器测试

- **基础任务列表**：测试 `[ ]` 和 `[x]` 格式的转换
- **嵌套任务列表**：验证缩进和层次结构
- **混合内容**：任务列表与其他 Markdown 元素的组合
- **复杂表格**：表格与任务列表的混合使用

### 2. 转换验证

- **Markdown → JSON**：检查 ProseMirror JSON 生成
- **JSON → Markdown**：验证往返转换一致性
- **编辑器预览**：实时查看渲染效果
- **HTML 转换**：验证 HTML 格式支持

### 3. 数据迁移工具

- **批量迁移**：将现有 Markdown 数据转换为新格式
- **进度跟踪**：实时显示迁移进度
- **错误处理**：识别和报告转换问题

## 使用步骤

1. **启动开发服务器**

   ```bash
   npm run dev
   ```

2. **访问测试页面**
   打开 `http://localhost:5173/?test=conversion-test`

3. **选择测试模式**

   - 预设测试案例：选择内置的测试样例
   - 自定义输入：输入自己的 Markdown 内容

4. **运行测试**
   点击"运行测试"按钮，查看转换结果

5. **检查结果**
   - 编辑器预览：查看实际渲染效果
   - JSON 结果：检查生成的 ProseMirror JSON
   - 往返测试：验证转换一致性

## 注意事项

### 现有项目代码逻辑

✅ **保持兼容性：**

- 现有的 Markdown 存储格式继续支持
- 编辑器接口保持不变
- 用户界面无显著变化

### 性能优化

- 原生 TipTap 转换比 MarkdownIt + Turndown 更高效
- 减少了第三方依赖
- 更好的类型安全性

### 错误处理

- 转换失败时提供详细错误信息
- 支持回退到简单文本格式
- 数据迁移包含错误恢复机制

## 技术细节

### 核心依赖

- `@tiptap/core` - 核心编辑器功能
- `@tiptap/extension-task-list` - 任务列表支持
- `@tiptap/extension-task-item` - 任务项支持
- `@tiptap/extension-table` - 表格支持

### 数据格式

- **旧格式**：纯 Markdown 字符串
- **新格式**：ProseMirror JSON + Markdown 备份
- **兼容性**：两种格式都可正常使用

### 迁移策略

1. 渐进式迁移：用户可选择迁移时机
2. 数据安全：原始数据保持不变
3. 回滚支持：可恢复到原始格式

## 验证清单

在测试完成后，请确认以下功能正常：

- [ ] 基础任务列表 `[ ]` 和 `[x]` 显示正确
- [ ] 嵌套任务列表保持正确缩进
- [ ] 任务状态可以正常切换
- [ ] 表格内容正确渲染
- [ ] 多级标题格式保持
- [ ] 有序和无序列表正常显示
- [ ] 文本格式（加粗、斜体）正确
- [ ] 往返转换保持内容一致性
- [ ] 编辑器交互流畅无卡顿
- [ ] 错误情况下有合理提示

## 下一步

测试完成后，新的转换系统就可以正式投入使用。主要优势包括：

1. **更好的一致性**：原生 TipTap 转换确保编辑器和存储格式完全同步
2. **功能丰富**：支持任务列表、表格等复杂内容
3. **性能提升**：减少转换步骤，提高响应速度
4. **维护性**：更简洁的代码结构，更容易扩展新功能

通过这个完全原生化的解决方案，Infinity Notes 现在拥有了更强大、更可靠的内容处理能力。
