# 无限便签 - 用户使用手册

## 📑 目录

- [📖 软件简介](#-软件简介)
- [🚀 快速入门](#-快速入门)
- [📝 便签管理](#-便签管理)
- [🎨 画布操作](#-画布操作)
- [🤖 AI 智能功能](#-ai智能功能)
- [🔗 连接与关系](#-连接与关系)
- [⚙️ 设置与配置](#️-设置与配置)
- [⌨️ 快捷键大全](#️-快捷键大全)
- [🌐 多页面架构](#-多页面架构)
- [🧪 测试与调试](#-测试与调试)
- [🔧 高级功能](#-高级功能)
- [💡 使用场景与最佳实践](#-使用场景与最佳实践)
- [🎯 效率提升技巧](#-效率提升技巧)
- [🔐 隐私与安全](#-隐私与安全)
- [📈 性能优化建议](#-性能优化建议)
- [🚀 部署与构建](#-部署与构建)
- [🆕 版本更新与新功能](#-版本更新与新功能)
- [🛠️ 故障排除](#️-故障排除)
- [📞 技术支持](#-技术支持)

## 📖 软件简介

无限便签是一款基于 AI 技术的智能无限画布思维整理工具，让您在无边界的画布上自由创建、连接和整理想法。通过强大的 AI 智能汇总功能，将传统便签体验提升到全新高度。

### 🎯 核心价值

- **无限画布**：突破传统便签的空间限制，在无限大的画布上自由创作
- **AI 智能化**：集成多种 AI 模型，提供智能汇总、思维链分析等功能
- **可视化连接**：用连接线将相关便签串联，构建清晰的思维导图
- **本地存储**：所有数据存储在本地，保护隐私安全
- **高性能**：优化的渲染机制，支持大量便签流畅操作

## 🚀 快速入门

### 首次使用

1. **访问应用**

   - 打开浏览器访问应用地址
   - 首次访问会显示官网介绍页面
   - 点击"开始使用"进入应用主界面

2. **界面概览**

   - **主画布**：中央的无限画布区域，用于创建和管理便签
   - **工具栏**：顶部工具栏，包含缩放、设置等功能
   - **侧边栏**：右侧便签列表，可快速定位和管理便签
   - **控制台**：底部快速操作区域，支持 AI 生成和便签创建

3. **创建第一个便签**
   - 双击画布空白处
   - 或使用快捷键 `Ctrl+N` (Windows) / `Cmd+N` (Mac)
   - 或点击侧边栏的"新建便签"按钮

## 📝 便签管理

### 创建便签

**方法一：双击创建**

- 在画布空白处双击鼠标左键
- 便签会在点击位置创建
- 自动进入编辑模式

**方法二：快捷键创建**

- 按下 `Ctrl+N` (Windows) 或 `Cmd+N` (Mac)
- 便签会在画布中心创建
- 自动进入编辑模式

**方法三：侧边栏创建**

- 点击右侧侧边栏的"新建便签"按钮
- 便签会在画布中心创建

### 编辑便签

**标题编辑**

- 双击便签顶部标题区域
- 输入新标题后按回车或点击外部保存

**内容编辑**

- 双击便签内容区域进入编辑模式
- 支持完整的 Markdown 语法
- 实时预览渲染效果

**编辑快捷键**

- `Esc`：退出编辑模式（自动保存）
- `Ctrl+Enter` / `Cmd+Enter`：保存并退出编辑
- `Ctrl+S` / `Cmd+S`：保存当前内容

**Markdown 支持**

- 标题：`# 一级标题` `## 二级标题`
- 粗体：`**粗体文本**`
- 斜体：`*斜体文本*`
- 列表：`- 列表项` 或 `1. 有序列表`
- 链接：`[链接文本](URL)`
- 代码：`` `代码` `` 或 `代码块`

### 便签操作

**移动便签**

- 拖拽便签头部区域移动位置
- 支持精确的像素级定位
- 移动时显示实时坐标

**调整大小**

- 拖拽便签右下角的调整手柄
- 支持自由调整宽度和高度
- 最小尺寸限制确保内容可读性

**层级管理**

- 点击便签任意位置自动置顶
- 支持多层级便签重叠显示
- 智能层级管理避免遮挡

**颜色设置**

- 点击便签右上角的颜色按钮
- 提供 8 种预设颜色主题：
  - 黄色（默认）、蓝色、绿色、粉色、紫色
  - 橙色、青色、灰色
- 颜色会立即应用并保存

**删除便签**

- 点击便签右上角的删除按钮（×）
- 确认删除后便签将永久移除
- 支持批量删除多个便签

### 便签定位

**侧边栏定位**

- 打开右侧侧边栏查看便签列表
- 点击任意便签项自动定位到画布中央
- 便签会自动置顶显示，确保不被遮挡
- 支持流畅的定位动画效果

**搜索定位**

- 在侧边栏顶部搜索框输入关键词
- 实时过滤匹配的便签
- 支持标题和内容全文搜索
- 点击搜索结果快速定位

## 🎨 画布操作

### 画布导航

**缩放控制**

- 鼠标滚轮：向上滚动放大，向下滚动缩小
- 快捷键：`Ctrl/Cmd + +` 放大，`Ctrl/Cmd + -` 缩小
- 工具栏按钮：点击放大镜图标进行缩放
- 缩放范围：25% - 200%，支持 8 个预设级别

**画布拖拽**

- 按住鼠标中键拖拽画布
- 或按住空格键 + 鼠标左键拖拽
- 支持惯性滚动效果

**视图重置**

- 快捷键：`Ctrl/Cmd + 0` 重置缩放到 100%
- 工具栏重置按钮：快速回到默认视图

### 移动模式

**启用移动模式**

- 点击工具栏的移动模式按钮
- 或使用快捷键激活
- 移动模式下便签编辑被禁用，专注于布局调整

**移动模式特性**

- 便签无法进入编辑状态
- 可以自由拖拽移动便签位置
- 画布拖拽更加灵敏
- 顶部显示移动模式指示器

### 网格背景

**网格显示**

- 精美的网格背景辅助对齐和定位
- 可在设置中开启/关闭网格显示
- 网格会根据缩放级别自动调整密度

## 🤖 AI 智能功能

### AI 配置设置

**基础配置**

1. 点击工具栏设置按钮或使用快捷键 `Ctrl/Cmd + ,`
2. 选择"AI 设置"标签页
3. 配置以下信息：
   - **API 地址**：AI 服务的接口地址（如：https://api.openai.com/v1）
   - **API 密钥**：您的 API 密钥
   - **AI 模型**：选择要使用的 AI 模型（如：gpt-4o-mini）

**高级设置**

- **温度参数**：控制 AI 回复的随机性（0-2，推荐 0.7）
- **最大 Token 数**：限制 AI 回复的长度（推荐 1000-2000）
- **系统提示词**：自定义 AI 的角色和行为模式

### AI 角色模板

**预设角色**

1. **正常对话模式**：直接与 AI 对话，获得原始回复
2. **默认便签助手**：通用的便签生成助手
3. **工作任务助手**：专注于工作任务和项目管理
4. **学习笔记助手**：优化学习内容整理和知识管理
5. **生活规划助手**：个人生活规划和日常管理
6. **创意灵感助手**：激发创意思维和想法整理

**选择角色**

1. 在 AI 设置中点击"AI 提示设置"
2. 浏览热门推荐或完整角色列表
3. 点击选择合适的角色模板
4. 可以自定义修改提示词内容

### AI 汇总功能

**多便签汇总**

1. 按住 `Ctrl` 键点击多个便签进行多选
2. 选中的便签会显示选中状态（蓝色边框）
3. 点击控制台的"AI 汇总"按钮
4. AI 会分析所有选中便签的内容
5. 生成新的汇总便签，包含要点和总结

**汇总模式**

- **汇总模式**：保留原便签，生成新的汇总便签
- **替换模式**：用 AI 生成的内容替换选中的便签
- 支持溯源功能，可查看汇总的来源便签

**流式生成**

- AI 内容实时流式显示，无需等待完整响应
- 生成过程中便签显示流式动画效果
- 可以随时停止生成过程

### 思维链功能

**启用思维链**

1. 在 AI 设置中开启"显示思维过程"
2. 支持的 AI 模型会显示完整的思考步骤
3. 包含分析、推理、结论等思维过程

**思维链显示**

- 便签右上角显示思维链图标
- 点击图标查看完整的思考过程
- 支持步骤展开和折叠
- 显示思考时间和推理路径

### AI 测试功能

**连接测试**

- 在 AI 设置中点击"测试连接"
- 验证 API 配置是否正确
- 显示连接状态和错误信息

**思维链测试**

- 测试当前模型是否支持思维链功能
- 显示思维链步骤数量
- 帮助优化 AI 配置

## 🔗 连接与关系

### 创建连接

**手动连接**

1. 点击便签右下角的连接点（圆形图标）
2. 拖拽到目标便签建立连接
3. 连接线会自动计算最佳路径
4. 支持多种连接样式和颜色

**连接管理**

- 连接的便签会显示在便签插槽中
- 支持移除单个连接
- 支持清空所有连接
- 连接状态实时同步

### 溯源连接

**查看溯源**

1. 点击便签右上角的设置按钮（三个点）
2. 选择"显示溯源连接"
3. 查看便签的生成来源和依赖关系
4. 溯源连接用不同颜色和样式显示

**溯源信息**

- 显示源便签列表
- 支持查看原始内容（替换模式）
- 追踪便签的生成历史
- 理解便签间的逻辑关系

### 便签插槽

**插槽功能**

- 位于控制台上方的连接管理区域
- 显示当前连接的便签
- 支持快速移除连接
- 提供连接模式切换

**连接模式**

- **普通连接**：手动创建的便签连接
- **溯源连接**：AI 生成时的源便签连接
- 不同模式用不同颜色区分

## ⚙️ 设置与配置

### 外观设置

**主题配置**

- 浅色主题/深色主题切换
- 网格背景显示开关
- 便签默认颜色设置

**缩放设置**

- 最大缩放比例（50%-500%）
- 最小缩放比例（10%-100%）
- 缩放步长调整

**便签默认尺寸**

- 手动便签默认宽度/高度
- AI 便签默认宽度/高度
- 便签最小尺寸限制

### 性能设置

**虚拟化渲染**

- 大内容自动虚拟化
- 虚拟化阈值设置（字符数）
- 性能监控开关

**缓存管理**

- 内存缓存设置
- 缓存过期时间
- 清理缓存选项

### 数据管理

**本地存储**

- 基于 IndexedDB 的本地数据库
- 实时自动保存
- 数据安全加密

**导入导出**

- 导出所有便签数据
- 导入备份数据
- 支持 JSON 格式

**数据统计**

- 便签总数统计
- 存储空间使用情况
- 性能指标监控

## ⌨️ 快捷键大全

### 基础操作

| 快捷键             | 功能       | 说明                   |
| ------------------ | ---------- | ---------------------- |
| `Ctrl+N` / `Cmd+N` | 创建便签   | 在画布中心创建新便签   |
| `Ctrl+S` / `Cmd+S` | 保存       | 保存当前编辑内容       |
| `Ctrl+,` / `Cmd+,` | 设置       | 打开设置面板           |
| `Esc`              | 退出编辑   | 退出便签编辑模式       |
| `Ctrl+Enter`       | 保存并退出 | 保存便签内容并退出编辑 |

### 画布操作

| 快捷键             | 功能     | 说明                |
| ------------------ | -------- | ------------------- |
| `Ctrl++` / `Cmd++` | 放大     | 放大画布            |
| `Ctrl+-` / `Cmd+-` | 缩小     | 缩小画布            |
| `Ctrl+0` / `Cmd+0` | 重置缩放 | 重置画布缩放到 100% |
| `空格+拖拽`        | 移动画布 | 拖拽移动画布视图    |
| `鼠标滚轮`         | 缩放     | 滚轮缩放画布        |

### 高级操作

| 快捷键             | 功能       | 说明           |
| ------------------ | ---------- | -------------- |
| `Ctrl+K` / `Cmd+K` | 聚焦控制台 | 快速访问控制台 |
| `/`                | 聚焦控制台 | 快速访问控制台 |
| `Ctrl+A` / `Cmd+A` | 全选便签   | 选择所有便签   |
| `Ctrl+Z` / `Cmd+Z` | 撤销       | 撤销上一步操作 |
| `Ctrl+Y` / `Cmd+Y` | 重做       | 重做上一步操作 |

## 🌐 多页面架构

### 页面结构

**智能路由系统**

- **index.html**：智能路由分发器，根据访问历史自动跳转
- **landing.html**：现代化的官网引导页面，采用 Notion 风格设计
- **app.html**：应用主界面，包含完整的便签功能

**首次访问流程**

1. 用户首次访问会看到官网介绍页面
2. 了解产品功能和特性
3. 点击"开始使用"进入应用
4. 后续访问会直接进入应用界面

**页面特性**

- 响应式设计，适配不同设备
- 优化的加载性能
- 独立的资源管理
- SEO 友好的结构

### 访问方式

**直接访问**

- 主页：`/` 或 `/index.html`
- 应用：`/app.html`
- 官网：`/landing.html`

**URL 参数**

- 测试模式：`/app.html?test=prompt-template`
- 性能测试：`/app.html?runOptimizationTests=true`
- 开发模式：自动启用开发工具

## 🧪 测试与调试

### 内置测试功能

**AI 提示模板测试**

1. 访问 `http://localhost:5173/?test=prompt-template`
2. 测试不同 AI 角色模板的效果
3. 验证提示词配置的正确性
4. 调试 AI 响应质量

**性能优化测试**

1. 在 URL 中添加 `?runOptimizationTests=true`
2. 自动运行性能测试套件
3. 检测连接线更新性能
4. 监控内存泄漏情况

**虚拟化测试**

- 开发环境下自动显示虚拟化状态监控
- 实时显示渲染性能指标
- 监控内存使用情况
- 检测滚动性能

### 开发者工具

**性能监控面板**

- 显示操作耗时统计
- 慢查询警告提示
- 内存使用监控
- 缓存命中率统计

**调试控制台**

- 便签操作日志
- AI 调用记录
- 数据库操作追踪
- 错误信息详情

**生产环境预览**

- 模拟生产环境效果
- 隐藏开发工具
- 测试最终用户体验
- 验证性能表现

## 🔧 高级功能

### 批量操作

**多选便签**

- 按住 `Ctrl` 键点击多个便签
- 选中的便签显示蓝色边框
- 支持批量删除和 AI 汇总

**批量管理**

- 批量修改便签颜色
- 批量移动便签位置
- 批量导出便签内容

### 搜索功能

**全文搜索**

- 在侧边栏搜索框输入关键词
- 支持标题和内容搜索
- 实时过滤显示结果
- 支持正则表达式搜索

**高级搜索**

- 按颜色筛选便签
- 按创建时间排序
- 按更新时间排序
- 按便签大小筛选

### 性能优化

**虚拟化渲染**

- 大内容自动分页显示
- 减少内存占用
- 提升渲染性能
- 支持流畅滚动

**智能缓存**

- 多层缓存机制
- 自动清理过期缓存
- 优化数据库查询
- 提升响应速度

### 调试工具

**开发者模式**

- 在开发环境下显示性能监控
- 虚拟化状态监控
- 内存使用情况
- 数据库操作统计

**性能监控**

- 操作耗时统计
- 慢查询警告
- 内存使用监控
- 缓存命中率

## 🛠️ 故障排除

### 常见问题

**AI 功能无法使用**

1. 检查 API 配置是否正确
2. 验证 API 密钥是否有效
3. 确认网络连接正常
4. 查看控制台错误信息

**便签无法保存**

1. 检查浏览器存储权限
2. 清理浏览器缓存
3. 确认磁盘空间充足
4. 重新初始化数据库

**性能问题**

1. 启用虚拟化渲染
2. 清理过期缓存
3. 减少同时显示的便签数量
4. 关闭不必要的动画效果

**连接线显示异常**

1. 刷新页面重新加载
2. 检查便签位置是否正确
3. 清除所有连接后重新创建
4. 重置画布视图

### 数据恢复

**数据丢失**

1. 检查浏览器 IndexedDB 数据
2. 查看是否有自动备份
3. 尝试从导出文件恢复
4. 联系技术支持

**配置重置**

1. 在设置中点击"重置配置"
2. 清除浏览器本地存储
3. 重新配置 AI 设置
4. 重新导入便签数据

## 📞 技术支持

### 获取帮助

**在线文档**

- 查看完整的 API 文档
- 阅读架构设计文档
- 参考开发指南

**社区支持**

- GitHub Issues 反馈问题
- 参与社区讨论
- 贡献代码和建议

**联系方式**

- 项目主页：查看最新版本和更新
- 技术文档：获取详细的技术信息
- 问题反馈：报告 Bug 和功能建议

## 💡 使用场景与最佳实践

### 工作场景

**项目管理**

1. 创建项目总览便签，记录项目目标和关键信息
2. 为每个任务创建独立便签，使用不同颜色区分优先级
3. 用连接线将相关任务串联，形成项目流程图
4. 使用 AI 汇总功能生成项目进度报告

**会议记录**

1. 为每个议题创建便签，实时记录讨论要点
2. 使用 Markdown 格式整理会议内容
3. 会议结束后用 AI 汇总生成会议纪要
4. 将行动项用不同颜色便签标记

**头脑风暴**

1. 快速创建便签记录每个想法
2. 使用创意灵感助手 AI 角色激发更多想法
3. 用连接线将相关想法分组
4. 最终用 AI 汇总整理成完整方案

### 学习场景

**知识整理**

1. 为每个知识点创建便签，支持 Markdown 格式
2. 使用学习笔记助手 AI 角色优化内容结构
3. 建立知识点间的连接关系
4. 定期用 AI 汇总生成复习要点

**论文写作**

1. 收集资料时为每个参考文献创建便签
2. 记录关键观点和引用信息
3. 用连接线构建论文逻辑结构
4. 使用 AI 汇总生成章节大纲

**语言学习**

1. 创建词汇便签，记录新单词和例句
2. 按主题分类使用不同颜色
3. 建立词汇间的关联连接
4. 定期用 AI 生成复习计划

### 生活场景

**旅行规划**

1. 为每个目的地创建便签，记录景点信息
2. 使用生活规划助手 AI 角色优化行程
3. 用连接线规划旅行路线
4. 生成完整的旅行攻略

**健康管理**

1. 记录每日健康数据和感受
2. 创建运动计划和饮食记录
3. 用 AI 分析健康趋势
4. 生成个性化健康建议

**财务规划**

1. 记录收支明细和理财目标
2. 分类管理不同类型的财务信息
3. 用 AI 分析消费模式
4. 生成理财建议和预算方案

### 创意场景

**写作创作**

1. 记录灵感片段和故事想法
2. 使用创意灵感助手 AI 角色扩展想法
3. 构建人物关系和情节连接
4. 生成完整的创作大纲

**设计思考**

1. 收集设计灵感和参考资料
2. 记录设计要求和约束条件
3. 探索不同设计方案
4. 用 AI 评估和优化设计方案

## 🎯 效率提升技巧

### 快速操作技巧

**键盘流操作**

1. 熟练使用快捷键，减少鼠标操作
2. 使用 `/` 快速聚焦控制台
3. `Ctrl+N` 快速创建便签
4. `Esc` 快速退出编辑模式

**批量处理**

1. 使用 `Ctrl+A` 全选便签进行批量操作
2. 按住 `Ctrl` 多选相关便签进行 AI 汇总
3. 利用搜索功能快速定位目标便签
4. 使用颜色分类管理不同类型内容

**模板化工作**

1. 创建常用的便签模板
2. 使用 AI 角色模板快速生成特定类型内容
3. 建立标准的连接模式和布局
4. 保存常用的配置设置

### AI 使用技巧

**提示词优化**

1. 使用具体明确的描述
2. 提供足够的上下文信息
3. 指定期望的输出格式
4. 利用角色模板提升效果

**汇总策略**

1. 选择相关性强的便签进行汇总
2. 合理控制汇总的便签数量（建议 3-8 个）
3. 使用替换模式时注意备份原内容
4. 利用溯源功能追踪信息来源

**思维链应用**

1. 启用思维链查看 AI 推理过程
2. 学习 AI 的思考模式优化自己的思路
3. 利用思维链验证 AI 结论的可靠性
4. 参考思维链步骤完善自己的分析

### 组织管理技巧

**空间布局**

1. 按主题区域划分画布空间
2. 使用颜色编码区分不同类型内容
3. 保持相关便签的空间邻近性
4. 定期整理和优化布局

**版本管理**

1. 重要内容及时导出备份
2. 利用溯源功能追踪内容演变
3. 定期清理过期和无用便签
4. 保持便签内容的时效性

**协作准备**

1. 使用清晰的便签标题
2. 保持内容格式的一致性
3. 添加必要的说明和注释
4. 准备导出格式便于分享

## 🔐 隐私与安全

### 数据安全

**本地存储**

- 所有便签数据存储在浏览器本地 IndexedDB 中
- 不会上传到任何服务器
- 数据完全由用户控制

**API 安全**

- AI API 密钥加密存储在本地
- 仅在 AI 调用时使用，不会泄露
- 支持随时更换和删除 API 配置

**隐私保护**

- AI 调用时不保存用户数据到 AI 服务商
- 便签内容仅用于当次 AI 处理
- 不收集任何用户行为数据

### 数据备份

**定期备份**

1. 在设置中定期导出便签数据
2. 保存导出的 JSON 文件到安全位置
3. 建议每周或每月备份一次
4. 重要项目完成后立即备份

**多重备份**

1. 本地文件备份
2. 云存储备份（用户自行上传）
3. 移动设备备份
4. 打印重要内容作为物理备份

**恢复测试**

1. 定期测试备份文件的完整性
2. 验证导入功能的正常工作
3. 确保备份数据的可读性
4. 建立应急恢复流程

## 📈 性能优化建议

### 使用优化

**便签管理**

1. 避免创建过多空白便签
2. 定期清理不需要的便签
3. 合理控制单个便签的内容长度
4. 使用虚拟化渲染处理大内容

**画布操作**

1. 避免频繁的大幅度缩放
2. 合理安排便签布局，避免过度重叠
3. 使用移动模式进行批量布局调整
4. 定期重置画布视图到合适位置

**AI 使用**

1. 合理设置 AI 参数，避免过长响应
2. 避免频繁的 AI 调用
3. 使用合适的 AI 模型和角色
4. 监控 API 使用量和成本

### 浏览器优化

**性能设置**

1. 使用现代浏览器（Chrome、Firefox、Safari）
2. 确保浏览器版本为最新
3. 启用硬件加速
4. 定期清理浏览器缓存

**内存管理**

1. 关闭不必要的浏览器标签页
2. 定期重启浏览器释放内存
3. 监控内存使用情况
4. 避免同时运行过多应用

**存储管理**

1. 定期清理浏览器存储
2. 确保有足够的磁盘空间
3. 监控 IndexedDB 使用情况
4. 及时导出和清理旧数据

## 🚀 部署与构建

### 自动化部署脚本

**使用部署脚本**
项目提供了便捷的部署脚本 `scripts/deploy.sh`，支持多种操作：

```bash
# 启动开发服务器
./scripts/deploy.sh dev

# 构建生产版本
./scripts/deploy.sh build

# 预览生产版本
./scripts/deploy.sh preview

# 构建并预览
./scripts/deploy.sh serve

# 清理构建文件
./scripts/deploy.sh clean

# 显示帮助信息
./scripts/deploy.sh help
```

**脚本特性**

- 自动检查环境依赖
- 智能端口检测和 IP 显示
- 完整的构建流程（类型检查、代码规范、构建）
- 构建文件大小统计
- 彩色输出和进度提示

### 手动构建

**开发环境**

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:5173
```

**生产构建**

```bash
# 类型检查
npm run type-check

# 代码规范检查
npm run lint

# 构建项目
npm run build

# 预览构建结果
npm run preview
```

### 构建配置

**多页面构建**

- 支持三个独立页面的构建
- 智能分包策略优化加载性能
- 自动代码分割和压缩

**性能优化**

- 使用 esbuild 进行快速压缩
- 分离 vendor、antd、utils 等公共模块
- 生产环境禁用 sourcemap 减小体积

**部署要求**

- 静态文件服务器（Nginx、Apache 等）
- 支持 HTML5 History API
- HTTPS 推荐（PWA 功能需要）

### Nginx 配置

项目提供了完整的 Nginx 配置模板：

```nginx
# 基础配置
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;

    # 多页面路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
}
```

## 🆕 版本更新与新功能

### 更新机制

**自动更新**

- 应用会自动检查新版本
- 重要更新会显示通知
- 支持渐进式更新，无需重新安装

**更新内容**

- 新功能和改进
- 性能优化和 Bug 修复
- 安全更新和补丁
- 用户体验优化

### 新功能预览

**即将推出**

1. 标签管理系统
2. 模板应用功能
3. 协作共享功能
4. 移动端适配

**长期规划**

1. 多用户支持
2. 云同步功能
3. 插件系统
4. API 开放平台

---

_本手册详细介绍了无限便签的完整功能和使用方法。通过合理运用这些功能，您可以大大提升思维整理和知识管理的效率。如有任何问题或建议，欢迎通过 GitHub Issues 或其他渠道反馈。_
