# 无限便签桌面应用使用指南

## 🎉 构建成功！

您的无限便签桌面应用已经成功构建完成！以下是详细的使用指南。

## 📦 构建产物

构建完成后，您可以在 `dist-electron/` 目录中找到以下文件：

### macOS 版本
- **`Infinity Notes-0.1.0.dmg`** - Intel Mac 安装包 (140MB)
- **`Infinity Notes-0.1.0-arm64.dmg`** - Apple Silicon Mac 安装包 (135MB)
- **`Infinity Notes-0.1.0-mac.zip`** - Intel Mac 便携版 (135MB)
- **`Infinity Notes-0.1.0-arm64-mac.zip`** - Apple Silicon Mac 便携版 (130MB)

## 🚀 安装和运行

### 方式一：安装 DMG 包（推荐）
1. 双击对应的 `.dmg` 文件
2. 将 "Infinity Notes" 拖拽到 "Applications" 文件夹
3. 在启动台或应用程序文件夹中找到并启动应用

### 方式二：使用便携版
1. 解压对应的 `.zip` 文件
2. 双击解压后的 "Infinity Notes.app" 直接运行

## ✨ 桌面应用特性

### 🖥️ 窗口管理
- **智能窗口状态保存**：自动记住窗口大小、位置和最大化状态
- **最小化到系统托盘**：支持隐藏到系统托盘，不占用任务栏空间
- **单实例运行**：确保只有一个应用实例在运行

### ⌨️ 快捷键支持
- **Cmd/Ctrl + N**：新建便签
- **Cmd/Ctrl + S**：保存当前内容
- **Cmd/Ctrl + Shift + N**：全局快速创建便签
- **Cmd/Ctrl + Shift + I**：全局显示/隐藏窗口
- **Cmd/Ctrl + Plus/Minus**：放大/缩小画布
- **Cmd/Ctrl + 0**：重置画布缩放

### 🔔 系统集成
- **系统通知**：重要操作和状态变化的通知提醒
- **系统托盘菜单**：右键托盘图标可快速访问常用功能
- **原生菜单栏**：完整的原生应用菜单体验

### 💾 数据存储
- **本地数据存储**：使用 IndexedDB 本地存储，无需网络连接
- **设置持久化**：应用设置和偏好自动保存
- **数据安全**：所有数据存储在本地，保护隐私安全

## 🛠️ 开发和调试

### 开发模式运行
```bash
# 启动开发模式（热重载）
npm run electron:dev
```

### 重新构建
```bash
# 清理并重新构建
npm run clean
npm run build
npm run dist
```

### 构建特定平台
```bash
# 仅构建 macOS 版本
npm run dist:mac

# 仅构建 Windows 版本（需要在 Windows 环境）
npm run dist:win

# 仅构建 Linux 版本（需要在 Linux 环境）
npm run dist:linux
```

## 🔧 配置说明

### Electron 主要配置
- **主进程文件**：`electron/main.cjs`
- **预加载脚本**：`electron/preload.cjs`
- **构建配置**：`package.json` 中的 `build` 字段

### 应用信息
- **应用 ID**：`com.duobaobox.infinity-notes`
- **产品名称**：Infinity Notes
- **版本号**：0.1.0
- **分类**：生产力工具

## 🎯 核心功能

### 无限画布
- 支持无限滚动和缩放的画布
- 流畅的拖拽和移动体验
- 智能网格对齐

### 智能便签
- 多种颜色主题便签
- 富文本编辑支持
- 便签间智能连接

### AI 功能
- 智能内容提取和汇总
- 思维链分析
- 内容智能分类

### 数据管理
- 便签导入导出
- 批量操作支持
- 搜索和过滤

## 🐛 故障排除

### 应用无法启动
1. 确保系统版本兼容（macOS 10.15+）
2. 检查是否有安全软件阻止运行
3. 尝试在终端中运行以查看错误信息

### 数据丢失
- 应用数据存储在用户目录的 IndexedDB 中
- 可以通过开发者工具查看和备份数据

### 性能问题
- 检查便签数量是否过多
- 尝试重启应用清理内存
- 关闭不必要的后台应用

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. 查看 `docs/` 目录中的其他文档
2. 检查 GitHub Issues
3. 联系开发团队

## 🎊 恭喜！

您的无限便签桌面应用已经成功构建并可以使用了！享受这个强大的思维整理工具吧！

---

*构建时间：2025年7月25日*
*版本：v0.1.0*
