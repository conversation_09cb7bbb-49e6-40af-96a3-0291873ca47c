// 保留标准 vitest 导入即可
import { describe, test, expect } from "vitest";
import { defaultConverter } from "../src/utils/contentConverter";

describe("有序列表转换测试", () => {
  test("简单有序列表往返转换", () => {
    const input = "1. 第一项\n2. 第二项\n3. 第三项";
    const jsonResult = defaultConverter.markdownToJSON(input);
    expect(jsonResult.success).toBe(true);
    if (jsonResult.success && typeof jsonResult.content !== "string") {
      const roundTrip = defaultConverter.jsonToMarkdown(
        jsonResult.content as any
      );
      expect(roundTrip.success).toBe(true);
      if (roundTrip.success) {
        expect(roundTrip.content.trim()).toBe(input);
      }
    }
  });

  test("AI生成内容有序列表问题修复", () => {
    const input = "6. 免费！免费！🆓\n\n目前我完全免费使用，没有隐藏收费。";
    const jsonResult = defaultConverter.markdownToJSON(input);
    expect(jsonResult.success).toBe(true);
    if (jsonResult.success && typeof jsonResult.content !== "string") {
      const roundTrip = defaultConverter.jsonToMarkdown(
        jsonResult.content as any
      );
      expect(roundTrip.success).toBe(true);
      if (roundTrip.success) {
        expect(roundTrip.content).not.toContain("6. ,免费");
        expect(roundTrip.content).toContain("6. 免费！免费！🆓");
      }
    }
  });

  test("有序列表编号保持一致", () => {
    const input = "5. 第五项\n6. 第六项\n7. 第七项";
    const jsonResult = defaultConverter.markdownToJSON(input);
    expect(jsonResult.success).toBe(true);
    if (jsonResult.success && typeof jsonResult.content !== "string") {
      const roundTrip = defaultConverter.jsonToMarkdown(
        jsonResult.content as any
      );
      expect(roundTrip.success).toBe(true);
      if (roundTrip.success) {
        expect(roundTrip.content).toContain("5. 第五项");
        expect(roundTrip.content).toContain("6. 第六项");
        expect(roundTrip.content).toContain("7. 第七项");
      }
    }
  });

  test("混合内容转换测试", () => {
    const input = "这是段落。\n\n1. 列表项\n2. 另一项\n\n另一个段落。";
    const jsonResult = defaultConverter.markdownToJSON(input);
    expect(jsonResult.success).toBe(true);
    if (jsonResult.success && typeof jsonResult.content !== "string") {
      const roundTrip = defaultConverter.jsonToMarkdown(
        jsonResult.content as any
      );
      expect(roundTrip.success).toBe(true);
      if (roundTrip.success) {
        console.log("输入:", input);
        console.log("输出:", roundTrip.content);
        expect(roundTrip.content).toContain("1. 列表项");
        expect(roundTrip.content).toContain("2. 另一项");
      }
    }
  });
});
